#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 堆栈设备唤醒函数
 *
 * 根据指定方向唤醒AFE系统中的堆栈设备
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 唤醒方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_stack_wakeup(uintptr_t afe_ctx, int8_t dir)
{
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;
    un_COMM_CTRL_t comm_ctrl;
    un_CONTROL1_t control1;

    // 1. 软复位堆栈设备
    control1.u8Register = 0;
    control1.stcField.SOFT_RESET = 1;
    stack_write(R_CONTROL1, &control1.u8Register, 1); // 堆栈设备
    delay_us(220);

    // 2. 阻止下一个'发送到下一个'
    comm_ctrl.u8Register = 0;
    comm_ctrl.stcField.STACK_DEV = 1;
    comm_ctrl.stcField.TOP_STACK = 1;
    single_dev_write(1, R_COMM_CTRL, &comm_ctrl.u8Register, 1); // 1号设备，堆栈设备

    // 3. 设置基础方向
    shq_set_base_dir_bit(dir); // dir: 0为正向，1为反向

    // 4. 发送唤醒信号
    control1.u8Register = 0;
    control1.stcField.SEND_WAKE = 1;
    control1.stcField.DIR_SEL = dir ? 1 : 0;
    single_dev_write(0, R_CONTROL1, &control1.u8Register, 1); // 0号设备，基础设备

    // 5. 等待堆栈设备就绪
    simple_delay_ms((p_afe->stack_count + 1) * 8);
    
    // 6. 检查设备状态
    if (!dev_is_ready()) {
        print("inf:base not ready, clearing\n");
        dev_comm_clear();
        if (!dev_is_ready()) {
            print("inf:base error busy, dev_is_ready=%d\n", dev_is_ready());
            return COMME_BASE_ERROR;
        }
    }

    return 0;
}
