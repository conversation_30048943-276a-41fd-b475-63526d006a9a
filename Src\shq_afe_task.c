#include <stdint.h>
#include <string.h>

#include "shq_afe.h"
#include "platform/comm_io.h"
#include "shq89718/shq89xx.h"
#include "shq_afe_task.h"
#include "shq_timer.h"

// 简化的任务数据结构
static SHQ_AFE_DATA g_afe_data;
static int8_t g_afe_running = 0;

// 简化的命令处理函数
int32_t cmd_proc(uint32_t cmd, uint8_t *buf, int32_t buf_size, int8_t *rep, uint32_t argc, uint8_t *argv[])
{
    // 简化命令处理，只保留基本的启停控制
    if (cmd == '0dmc') {
        g_afe_running = 0;  // 停止
        return 0;
    } else if (cmd == '1dmc') {
        g_afe_running = 1;  // 启动
        return 0;
    } else if (cmd == 'rdmc') {
        // 重启AFE系统
        shq_afe_simple_restart();
        return 0;
    }

    return -1; // 命令未处理
}

static const uint8_t afe_cell_counts[] = {18, 18, 18, 18, 18, 18, 18, 18};	 // cell count for each stack
static const uint8_t afe_bus_bar[] = {100, 100, 100, 100, 100, 100, 100, 100}; // bus bar position
static const uint8_t afe_gp_ch_counts[] = {11, 11, 11, 11, 11, 11, 11, 11};

extern void shq89718_global_init(void);

// 简化的初始化函数
void shq_afe_task_init(void)
{
    // 清零数据结构
    memset(&g_afe_data, 0, sizeof(g_afe_data));

    // 设置基本配置
    g_afe_data.stack_count = STACK_COUNT;
    g_afe_data.cell_counts = afe_cell_counts;
    g_afe_data.bus_bar_pos = afe_bus_bar;
    g_afe_data.gp_ch_counts = afe_gp_ch_counts;

    // 初始化诊断专用缓存区
    memset(g_afe_data.diag_volts_vc, 0, sizeof(g_afe_data.diag_volts_vc));
    memset(g_afe_data.diag_volts_cb, 0, sizeof(g_afe_data.diag_volts_cb));
    memset(g_afe_data.diag_volts_gp, 0, sizeof(g_afe_data.diag_volts_gp));

    // 初始化底层全局设置
    shq89718_global_init();

    // 设置为停止状态
    g_afe_running = 0;
}

// 简化的重启函数
void shq_afe_simple_restart(void)
{
    // 清除错误状态
    g_afe_data.stack_err_addr_fwd = 0;
    g_afe_data.stack_err_addr_bwd = 0;
    g_afe_data.stack_count_fwd = 0;
    g_afe_data.stack_count_bwd = 0;
    g_afe_data.comm_dir_state = 0;
    g_afe_data.dsc_stack_comm_err = 0;

    // 清零诊断专用缓存区
    memset(g_afe_data.diag_volts_vc, 0, sizeof(g_afe_data.diag_volts_vc));
    memset(g_afe_data.diag_volts_cb, 0, sizeof(g_afe_data.diag_volts_cb));
    memset(g_afe_data.diag_volts_gp, 0, sizeof(g_afe_data.diag_volts_gp));

    // 停止运行
    g_afe_running = 0;
}

// 简化的AFE唤醒序列
int32_t shq_afe_simple_wakeup(int8_t dir)
{
    int32_t ret;

    print("inf: 开始AFE唤醒序列\n");

    // 1. 唤醒基础设备
    print("inf: 1. 唤醒基础设备\n");
    ret = shq_base_wakeup();
    if (ret < 0) {
        print("err: 基础设备唤醒失败: %d\n", ret);
        return ret;
    }

    // 2. 基础配置
    print("inf: 2. 基础设备配置\n");
    ret = shq_base_config(dir);
    if (ret < 0) {
        print("err: 基础设备配置失败: %d\n", ret);
        return ret;
    }

    // 3. 唤醒堆栈设备 - 正向
    print("inf: 3. 唤醒堆栈设备\n");
    ret = shq_stack_wakeup((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 堆栈设备唤醒失败: %d\n", ret);
        return ret;
    }

    // 4. 地址分配 - 正向
    print("inf: 4. 地址分配\n");
    ret = shq_addressing((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 地址分配失败: %d\n", ret);
        return ret;
    }

    // 5. 初始化设备
    print("inf: 5. 设备初始化\n");
    ret = shq_init((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 设备初始化失败: %d\n", ret);
        return ret;
    }

    print("inf: AFE唤醒序列完成\n");
    return 0;
}

// 简化的采样函数
int32_t shq_afe_simple_sampling(int8_t dir)
{
    int32_t ret;

    // 电芯电压采样
    print("inf: 开始电芯电压采样\n");
    ret = shq_cs_sampling((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 电芯电压采样失败: %d\n", ret);
        return ret;
    }

    // GPIO温度采样
    print("inf: 开始GPIO温度采样\n");
    ret = shq_gp_sampling((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: GPIO温度采样失败: %d\n", ret);
        return ret;
    }

    // 断线诊断
    print("inf: 开始断线诊断\n");
    ret = diag_cell_wrie_disconnection((uintptr_t)&g_afe_data, dir);
    if (ret < 0) {
        print("err: 断线诊断失败: %d\n", ret);
        return ret;
    }

    print("inf: 采样完成\n");
    return 0;
}

// 简化的掉电函数
int32_t shq_afe_simple_power_down(void)
{
    int32_t ret;

    print("inf: 开始AFE系统掉电\n");

    // 停止AFE运行
    g_afe_running = 0;

    // 执行掉电序列
    ret = shq_power_down();
    if (ret < 0) {
        print("err: AFE系统掉电失败: %d\n", ret);
        return ret;
    }

    print("inf: AFE系统掉电完成\n");
    return 0;
}

// 简化的主运行函数
int32_t shq_afe_task_run(void)
{
    static int8_t afe_initialized = 0;
    int32_t ret;
    int8_t dir = 0;

    // 如果没有运行，直接返回
    if (!g_afe_running) {
        afe_initialized = 0;
        return 0;
    }

    // 如果还没初始化，先进行唤醒和初始化
    if (!afe_initialized) {
        print("inf: 开始唤醒和初始化\n");
        ret = shq_afe_simple_wakeup(dir);
        if (ret < 0) {
            // 初始化失败，等待一段时间后重试
            simple_delay_ms(500);
            return ret;
        }
        afe_initialized = 1;
    }

    // 执行采样
    print("inf: 开始采样\n");
    ret = shq_afe_simple_sampling(dir);
    if (ret < 0) {
        // 采样失败，重新初始化
        afe_initialized = 0;
        return ret;
    }

    // 掉电
    print("inf: 开始AFE系统掉电\n");
    ret = shq_afe_simple_power_down();
    if (ret < 0) {
        // 掉电失败，等待一段时间后重试
        simple_delay_ms(500);
        return ret;
    }

    print("inf: 等待下个指令\n");
    simple_delay_ms(1000); // 等待下个指令，1秒钟后开始下一轮循环，可以根据实际情况调整延迟时间
    
    return 0;
}

