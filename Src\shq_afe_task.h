#ifndef HEADER_SHQ_AFE_TASK_H
#define HEADER_SHQ_AFE_TASK_H

#include <stdint.h>
#include <string.h>
#include "shq_afe.h"

#ifdef __cplusplus
extern "C"
{
#endif

// 简化的函数接口
void shq_afe_task_init(void);
int32_t shq_afe_task_run(void);

// 新增的简化函数
void shq_afe_simple_restart(void);
int32_t shq_afe_simple_wakeup(int8_t dir);
int32_t shq_afe_simple_sampling(int8_t dir);
int32_t shq_afe_simple_power_down(void);

#ifdef __cplusplus
}
#endif

#endif
