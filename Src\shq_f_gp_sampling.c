#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 堆栈通道索引计算
 *
 * 计算指定堆栈索引的全局通道起始索引
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @return 全局通道起始索引
 */
static int32_t simple_stack_ch_index(SHQ_AFE_DATA *p_afe, int8_t stack_index)
{
    (void)p_afe; // 简化版本不使用p_afe参数
    return 11 * stack_index;
}

/**
 * @brief GPIO数值保存函数
 *
 * 保存指定堆栈的GPIO ADC数值到数据结构中
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @param ch_index 全局通道索引
 * @param pReadBuffer 读取缓冲区指针
 * @return 下一个全局通道索引
 */
static int32_t simple_save_gp_values(SHQ_AFE_DATA *p_afe, int8_t stack_index, int32_t ch_index, uint8_t *pReadBuffer)
{
    int16_t code;
    int32_t volt_mv;

    for (int8_t j = 0; j < p_afe->gp_ch_counts[stack_index]; j++) {
        // 处理GPIO电压
        code = (pReadBuffer[j * 2] << 8) + pReadBuffer[j * 2 + 1];
        volt_mv = shq_code_to_mV(code);

#if defined(KEEP_ALL_DEV_VOLTS)
        p_afe->all_dev_volts_gp[stack_index][j] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->all_dev_code_gp[stack_index][j] = code;
#endif
#endif

#if defined(KEEP_INDEXED_VOLTS)
        p_afe->indexed_gp_volts[ch_index] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->indexed_gp_code[ch_index] = code;
#endif
#endif
        ch_index++;
    }
    return ch_index;
}

/**
 * @brief GPIO/温度采样函数
 *
 * 执行指定方向的GPIO ADC采样并保存结果
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 采样方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_gp_sampling(uintptr_t afe_ctx, int8_t dir)
{
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;
    int32_t ret = 0;
    uint8_t *pReadBuffer;
    int32_t ch_index;
    int8_t stack_count;
    int8_t stack_index;

    // 1. 设置ADC模式并启动GPIO采样
    shq_set_adc_mode(DEV_ADDR_STACKS, 0, 1, 0); // round robin once
    shq_set_adc_start(DEV_ADDR_STACKS, 0, 1);

    // 2. 等待ADC转换完成（12ms）
    simple_delay_ms(12);
    // 3. 获取堆栈计数
    stack_count = dir ? shq_get_bw_stack_count(p_afe) : shq_get_fw_stack_count(p_afe);

    if (stack_count < 1) {
        return 0;
    }

    // 4. 尝试堆栈读取
    ret = stack_read(R_GP1V_HI, 22, stack_count, &pReadBuffer);

    if (ret > 0) {
        // 堆栈读取成功
        if (dir) {
            pReadBuffer += DSC_FRAME_DATA_OFFSET;
            stack_index = p_afe->stack_count - p_afe->stack_count_bwd;
            ch_index = simple_stack_ch_index(p_afe, stack_index);
        } else {
            pReadBuffer += ((22 + DSC_RESP_NONE_DATA_BYTES) * (stack_count - 1) + DSC_FRAME_DATA_OFFSET);
            stack_index = 0;
            ch_index = 0;
        }

        // 处理所有堆栈的数据
        while (1) {
            ch_index = simple_save_gp_values(p_afe, stack_index, ch_index, pReadBuffer);

            if (dir) {
                stack_index++;
                if (stack_index >= p_afe->stack_count) {
                    break;
                }
                pReadBuffer += (22 + DSC_RESP_NONE_DATA_BYTES);
            } else {
                stack_index++;
                if (stack_index >= p_afe->stack_count_fwd) {
                    break;
                }
                pReadBuffer -= (22 + DSC_RESP_NONE_DATA_BYTES);
            }
        }

        // 跳过单独读取，直接到结束
        goto gp_sampling_end;
    } else {
        // 堆栈读取失败，使用单独读取恢复
        dev_comm_clear();
        simple_delay_ms(1); // 调度延时
    }

    // 5. 单独读取每个堆栈
    stack_count = dir ? shq_get_bw_stack_count(p_afe) : shq_get_fw_stack_count(p_afe);

    if (dir) {
        stack_index = p_afe->stack_count - 1;
        ch_index = simple_stack_ch_index(p_afe, stack_index);
    } else {
        stack_index = 0;
        ch_index = 0;
    }

    for (int8_t i = 1; i <= stack_count; i++) {
        ret = shq_reg_read(i, R_GP1V_HI, 22, &pReadBuffer);

        if (ret != 22) {
            // 单独读取失败
            dev_comm_clear();

            if (dir) {
                p_afe->stack_err_addr_bwd = i;
            } else {
                p_afe->stack_err_addr_fwd = i;
            }
            break;
        }

        // 保存GPIO数值
        ch_index = simple_save_gp_values(p_afe, stack_index, ch_index, pReadBuffer);

        if (dir) {
            stack_index--;
            ch_index = simple_stack_ch_index(p_afe, stack_index);
        } else {
            stack_index++;
        }
    }

gp_sampling_end:
    // 6. 采样完成，可选的调试输出
#if defined(KEEP_ALL_DEV_VOLTS)
    // 可以在这里添加调试输出
    for (int i = 0; i < p_afe->stack_count; i++) {
        for (int j = 0; j < p_afe->gp_ch_counts[i]; j++) {
            print("inf: all_dev_volts_gp[%d][%d] = %d\n", i, j, p_afe->all_dev_volts_gp[i][j]);
        }
    }
#endif

#if defined(KEEP_INDEXED_VOLTS)
    // 可以在这里添加索引GPIO电压的调试输出
    // print("inf:%d %d %d %d %d\n", p_afe->indexed_gp_volts[1], ...);
#endif

    return 0;
}


