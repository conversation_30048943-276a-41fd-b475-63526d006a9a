Current Version 1.02


文件说明:
platform:
    *  移植主要修改这些文件
    1. comm_io.h 包含所有和平台相关的定义
    2. comm_io_xx.c 对应平台相关的函数的实现
shq89718:
    1. crc 和 shq89xx 是底层通讯协议，包含比如单设备/广播/设备读写等
    2. shq8900_registers 和 shq89718_registers 是对寄存器的定义。shq89718_registers.c是寄存器图memmap暂时没用
    3. shq89718是对shq89718相关的各种操作，把对应的操作转换为寄存器读写的各种函数
当前目录:
    *  业务相关的各种流程。每个shq_f开头的文件包含一个完整的流程
    *  实现业务流程需要的依赖，shq_timer配合shq_f中的状态机替代msleep以便于在不支持msleep的系统中使用
    *  在有msleep实现的系统，必须保证msleep的时间>=请求的sleep时间
    1. shq_timer 是一个简易的timer，主要用于没有msleep的系统，这个timer的实现依赖于系统的1mS时钟的周期调用on_system_tick
	2. shq_afe.h 是这部分流程总的头文件，shq_afe.c包含公用的一些函数
    3. shq_f_base_pulses 是用于调试目的，发送必要的脉冲，然后示波器或者逻辑分析仪测量脉冲的时序比如宽度是否符合要求
	4. shq_f_base_wakeup 唤醒桥片
    5. shq_f_stack_wakeup 唤醒AFE设备
    6. shq_f_addressing 分配地址
    7. shq_f_init 初始化参数，比如设置参数，设置地址，设置模式等
    8. shq_f_cs_sampling 电芯采样的一个例子，这种一般就是周期性调用了，比如100mS一次
    9. shq_f_gp_sampling GPIO采样的例子
    10.shq_f_gp_with_mux 带MUX控制的GPIO采样的例子
    11.shq_f_power_down 让系统掉电，AFE设备2秒通讯超时自动掉电，桥片base则是使用shutdown脉冲使其进入掉电状态
    12.shq_f_base_power_down 使用shutdown脉冲使桥片base进入掉电状态
    13.shq_f_sleep_ms 一个msleep的流程
    14.shq_afe_task 和 shq_t_tests可以作为一些调用的参考

初始化流程:
shq_f_base_wakeup --> shq_f_stack_wakeup --> shq_f_addressing --> shq_f_init

工作循环:
shq_f_cs_sampling --> shq_f_gp_sampling --> check error:
    no error: continue
    error: shq_f_power_down --> 初始化流程

结束工作:
shq_f_power_down

双向初始化流程:
* 如果硬件有反向通讯链路(ring结构), 则注意初始化的时候，双向初始化以便于跳过失效的节点
shq_f_base_wakeup (dir = 0) --> shq_f_stack_wakeup (dir = 0) --> shq_f_addressing (dir = 0) --> shq_f_init (dir = 0) -->
shq_f_base_wakeup (dir = 1) --> shq_f_stack_wakeup (dir = 1) --> shq_f_addressing (dir = 1) --> shq_f_init (dir = 1)

双向初始化完成后，断链情况下的方向调整，不做重新addressing。
建议间隔一定时间之后，重新做双向初始化流程，如果断链的点已经恢复，则可以回归正常状态
shq_re_dir

双向初始化完成后，没有断链时的双向通讯方向调整
shq_set_dir_bit --> shq_addressing
