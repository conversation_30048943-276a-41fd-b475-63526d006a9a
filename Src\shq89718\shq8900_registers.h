#ifndef SHQ8900_REGISTERS_HDR
#define SHQ8900_REGISTERS_HDR

#include <stdint.h>

// Register addresses and types with default (reset) values
#define R_PRODUCT_ID        0x0000  // R, default 0x11
#define R_COMM_CONF         0x0001  // R/W, default 0x00
#define R_DEV_CONF          0x0002  // R/W, default 0x00
#define R_SLP_TIMEOUT       0x0003  // R/W, default 0x03
#define R_COMM_TIMEOUT      0x0004  // R/W, default 0x34
#define R_BIST_CTRL         0x0005  // W, default 0x00
#define R_BIST_RESULT       0x0006  // R, default 0x00
#define R_FAULT_SYS         0x0007  // R, default 0x00
#define R_FAULT_PWR         0x0008  // R, default 0x00
#define R_FAULT_COMM1       0x0009  // R, default 0x00
#define R_FAULT_COMM2       0x000A  // R, default 0x00
#define R_FAULT_RST         0x000B  // R, default 0x00
#define R_MASK_FAULT        0x000C  // R/W, default 0x00
#define R_SUMMARY_FAULT     0x000D  // R, default 0x00
#define R_DAISY_CTL         0x0010  // R/W, default 0x33
#define R_STACK_DEVICE      0x003E  // R, default 0x00
#define R_LPCM_STAT         0x003F  // R, default 0x00
#define R_DIR0_ADDR         0x0106  // R/W, default 0x00
#define R_DIR1_ADDR         0x0107  // R/W, default 0x00
#define R_CONTROL1          0x0109  // W, default 0x00

// PRODUCT_ID register (Address: 0x0000, 8-bit, Default 0x11)
typedef struct stc_PRODUCT_ID_field {
	uint8_t REV_ID  : 3;  // Bits [2:0]
	uint8_t CHIP_ID : 5;  // Bits [7:3]
} stc_PRODUCT_ID_field_t;

typedef union un_PRODUCT_ID {
    uint8_t u8Register;  // Default 0x11
    stc_PRODUCT_ID_field_t stcField;
} un_PRODUCT_ID_t;

// COMM_CONF register (Address: 0x0001, 8-bit, Default 0x00)
typedef struct stc_COMM_CONF_field {
	uint8_t PROT_SLP_CYCLE : 3;  // Bits [2:0]
	uint8_t LPCM_EN      : 1;  // Bit [3]
	uint8_t TWO_STOP_EN  : 1;  // Bit [4]
	uint8_t rsv_7_5 : 3;  // Bits [7:5] - Reserved
} stc_COMM_CONF_field_t;

typedef union un_COMM_CONF {
    uint8_t u8Register;  // Default 0x00
    stc_COMM_CONF_field_t stcField;
} un_COMM_CONF_t;

// DEV_CONF register (Address: 0x0002, 8-bit, Default 0x00)
typedef struct stc_DEV_CONF_field {
	uint8_t INH_DIS      : 2;  // Bits [1:0]
	uint8_t CLK_MON_EN   : 1;  // Bit [2]
	uint8_t rsv_7_3 : 5;  // Bits [7:3] - Reserved
} stc_DEV_CONF_field_t;

typedef union un_DEV_CONF {
    uint8_t u8Register;  // Default 0x00
    stc_DEV_CONF_field_t stcField;
} un_DEV_CONF_t;

// SLP_TIMEOUT register (Address: 0x0003, 8-bit, Default 0x03)
typedef struct stc_SLP_TIMEOUT_field {
	uint8_t SLP_TIME     : 3;  // Bits [2:0]
	uint8_t rsv_7_3 : 5;  // Bits [7:3] - Reserved
} stc_SLP_TIMEOUT_field_t;

typedef union un_SLP_TIMEOUT {
    uint8_t u8Register;  // Default 0x03
    stc_SLP_TIMEOUT_field_t stcField;
} un_SLP_TIMEOUT_t;

// COMM_TIMEOUT register (Address: 0x0004, 8-bit, Default 0x34)
typedef struct stc_COMM_TIMEOUT_field {
	uint8_t CTL_TIME     : 3;  // Bits [2:0]
	uint8_t CTL_ACT      : 1;  // Bit [3]
	uint8_t CTS_TIME     : 3;  // Bits [6:4]
	uint8_t rsv_7   : 1;  // Bit [7] - Reserved
} stc_COMM_TIMEOUT_field_t;

typedef union un_COMM_TIMEOUT {
    uint8_t u8Register;  // Default 0x34
    stc_COMM_TIMEOUT_field_t stcField;
} un_COMM_TIMEOUT_t;

// BIST_CTRL register (Address: 0x0005, 8-bit, Default 0x00)
typedef struct stc_BIST_CTRL_field {
	uint8_t INH_SET_GO   : 1;  // Bit [0]
	uint8_t PWR_BIST_GO  : 1;  // Bit [1]
	uint8_t PWR_BIST_CLR : 1;  // Bit [2]
	uint8_t rsv_7_3 : 5;  // Bits [7:3] - Reserved
} stc_BIST_CTRL_field_t;

typedef union un_BIST_CTRL {
    uint8_t u8Register;  // Default 0x00
    stc_BIST_CTRL_field_t stcField;
} un_BIST_CTRL_t;

// BIST_RESULT register (Address: 0x0006, 8-bit, Default 0x00)
typedef struct stc_BIST_RESULT_field {
	uint8_t INH_STAT      : 1;  // Bit [0]
	uint8_t PWR_BIST_DONE : 1;  // Bit [1]
	uint8_t PWR_BIST_FAIL : 1;  // Bit [2]
	uint8_t rsv_7_3 : 5;  // Bits [7:3] - Reserved
} stc_BIST_RESULT_field_t;

typedef union un_BIST_RESULT {
    uint8_t u8Register;  // Default 0x00
    stc_BIST_RESULT_field_t stcField;
} un_BIST_RESULT_t;

// FAULT_SYS register (Address: 0x0007, 8-bit, Default 0x00)
typedef struct stc_FAULT_SYS_field {
	uint8_t INH          : 1;  // Bit [0]
	uint8_t TSHUT        : 1;  // Bit [1]
	uint8_t CTS          : 1;  // Bit [2]
	uint8_t CTL          : 1;  // Bit [3]
	uint8_t DRST         : 1;  // Bit [4]
	uint8_t CONF_MON_ERR : 1;  // Bit [5]
	uint8_t rsv_7_6 : 2;  // Bits [7:6] - Reserved
} stc_FAULT_SYS_field_t;

typedef union un_FAULT_SYS {
    uint8_t u8Register;  // Default 0x00
    stc_FAULT_SYS_field_t stcField;
} un_FAULT_SYS_t;

// FAULT_PWR register (Address: 0x0008, 8-bit, Default 0x00)
typedef struct stc_FAULT_PWR_field {
	uint8_t DVDD_OV      : 1;  // Bit [0]
	uint8_t CVDD_OV      : 1;  // Bit [1]
	uint8_t rsv_3_2   : 2;  // Bit [3:2] - Reserved
	uint8_t AVAOREF_OV   : 1;  // Bit [4]
	uint8_t VIO_UV       : 1;  // Bit [5]
	uint8_t CVDD_UV_DRST : 1;  // Bit [6]
	uint8_t rsv_7 : 1;  // Bits [7] - Reserved
} stc_FAULT_PWR_field_t;

typedef union un_FAULT_PWR {
    uint8_t u8Register;  // Default 0x00
    stc_FAULT_PWR_field_t stcField;
} un_FAULT_PWR_t;

// FAULT_COMM1 register (Address: 0x0009, 8-bit, Default 0x00)
typedef struct stc_FAULT_COMM1_field {
	uint8_t RXFIFO_OF   : 1;  // Bit [0]
	uint8_t TXFIFO_UF    : 1;  // Bit [1]
	uint8_t TXFIFO_OF    : 1;  // Bit [2]
	uint8_t RXDATA_UNEXP : 1;  // Bit [3]
	uint8_t TXDATA_UNEXP : 1;  // Bit [4]
	uint8_t rsv_5 : 1;  // Bit [5] - Reserved
	uint8_t COMM_CLR_DET : 1;  // Bit [6]
	uint8_t rsv_7 : 1;  // Bits [7] - Reserved
} stc_FAULT_COMM1_field_t;

typedef union un_FAULT_COMM1 {
    uint8_t u8Register;  // Default 0x00
    stc_FAULT_COMM1_field_t stcField;
} un_FAULT_COMM1_t;

// FAULT_COMM2 register (Address: 0x000A, 8-bit, Default 0x00)
typedef struct stc_FAULT_COMM2_field {
	uint8_t FRAME_CRC_ERROR : 1;  // Bit [0]
	uint8_t UART_STOP_FAULT : 1;  // Bit [1]
	uint8_t rsv_7_2 : 6;  // Bits [7:2] - Reserved
} stc_FAULT_COMM2_field_t;

typedef union un_FAULT_COMM2 {
    uint8_t u8Register;  // Default 0x00
    stc_FAULT_COMM2_field_t stcField;
} un_FAULT_COMM2_t;

// FAULT_RST register (Address: 0x000B, 8-bit, Default 0x00)
typedef struct stc_FAULT_RST_field {
	uint8_t RST_SYS     : 1;  // Bit [0]
	uint8_t RST_PWR      : 1;  // Bit [1]
	uint8_t RST_COMM     : 1;  // Bit [2]
	uint8_t RST_FUSE_CRC_ERR : 1;  // Bit [3]
	uint8_t RST_OSC_ERR  : 1;  // Bit [4]
	uint8_t rsv_7_5 : 3;  // Bits [7:5] - Reserved
} stc_FAULT_RST_field_t;

typedef union un_FAULT_RST {
    uint8_t u8Register;  // Default 0x00
    stc_FAULT_RST_field_t stcField;
} un_FAULT_RST_t;

// MASK_FAULT register (Address: 0x000C, 8-bit, Default 0x00)
typedef struct stc_MASK_FAULT_field {
	uint8_t FAULT_SYS_MASK : 1;  // Bit [0]
	uint8_t FAULT_PWR_MASK : 1;  // Bit [1]
	uint8_t FAULT_COMM_MASK : 1;  // Bit [2]
	uint8_t FUSE_CRC_ERR_MASK : 1;  // Bit [3]
	uint8_t OSC_ERR_MASK : 1;  // Bit [4]
	uint8_t rsv_7_5 : 3;  // Bits [7:5] - Reserved
} stc_MASK_FAULT_field_t;

typedef union un_MASK_FAULT {
    uint8_t u8Register;  // Default 0x00
    stc_MASK_FAULT_field_t stcField;
} un_MASK_FAULT_t;

// SUMMARY_FAULT register (Address: 0x000D, 8-bit, Default 0x00)
typedef struct stc_SUMMARY_FAULT_field {
	uint8_t FAULT_SYS    : 1;  // Bit [0]
	uint8_t FAULT_PWR    : 1;  // Bit [1]
	uint8_t FAULT_COMM   : 1;  // Bit [2]
	uint8_t FUSE_CRC_ERR : 1;  // Bit [3]
	uint8_t OSC_ERR      : 1;  // Bit [4]
	uint8_t rsv_7_5 : 3;  // Bits [7:5] - Reserved
} stc_SUMMARY_FAULT_field_t;

typedef union un_SUMMARY_FAULT {
    uint8_t u8Register;  // Default 0x00
    stc_SUMMARY_FAULT_field_t stcField;
} un_SUMMARY_FAULT_t;

// DAISY_CTL register (Address: 0x0010, 8-bit, Default 0x33)
typedef struct stc_DAISY_CTL_field {
	uint8_t DAISY_RX_TH_ADJ_COML : 3;  // Bits [2:0]
	uint8_t rsv_3 : 1;  // Bits [3] - Reserved
	uint8_t DAISY_RX_TH_ADJ_COMH : 3;  // Bits [6:4]
	uint8_t rsv_7 : 1;  // Bits [7] - Reserved
} stc_DAISY_CTL_field_t;

typedef union un_DAISY_CTL {
    uint8_t u8Register;  // Default 0x33
    stc_DAISY_CTL_field_t stcField;
} un_DAISY_CTL_t;

// STACK_DEVICE register (Address: 0x003E, 8-bit, Default 0x00)
typedef struct stc_STACK_DEVICE_field {
	uint8_t STACK_DEV_NUM : 6;  // Bits [5:0]
	uint8_t rsv_7_6 : 2;  // Bits [7:6] - Reserved
} stc_STACK_DEVICE_field_t;

typedef union un_STACK_DEVICE {
    uint8_t u8Register;  // Default 0x00
    stc_STACK_DEVICE_field_t stcField;
} un_STACK_DEVICE_t;

// LPCM_STAT register (Address: 0x003F, 8-bit, Default 0x00)
typedef struct stc_LPCM_STAT_field {
	uint8_t RECV_HB_TONE : 1;  // Bit [0]
	uint8_t RECV_FLT_TONE : 1;  // Bit [1]
	uint8_t RECV_NO_TONE : 1;  // Bit [2]
	uint8_t FCOMM_DET    : 1;  // Bit [3]
	uint8_t rsv_7_4 : 4;  // Bits [7:4] - Reserved
} stc_LPCM_STAT_field_t;

typedef union un_LPCM_STAT {
    uint8_t u8Register;  // Default 0x00
    stc_LPCM_STAT_field_t stcField;
} un_LPCM_STAT_t;

// DIR0_ADDR register (Address: 0x0106, 8-bit, Default 0x00)
typedef struct stc_DIR0_ADDR_field {
	uint8_t ADDRESS      : 6;  // Bits [5:0]
	uint8_t rsv_7_6 : 2;  // Bits [7:6] - Reserved
} stc_DIR0_ADDR_field_t;

typedef union un_DIR0_ADDR {
    uint8_t u8Register;  // Default 0x00
    stc_DIR0_ADDR_field_t stcField;
} un_DIR0_ADDR_t;

// DIR1_ADDR register (Address: 0x0107, 8-bit, Default 0x00)
typedef struct stc_DIR1_ADDR_field {
	uint8_t ADDRESS      : 6;  // Bits [5:0]
	uint8_t rsv_7_6 : 2;  // Bits [7:6] - Reserved
} stc_DIR1_ADDR_field_t;

typedef union un_DIR1_ADDR {
    uint8_t u8Register;  // Default 0x00
    stc_DIR1_ADDR_field_t stcField;
} un_DIR1_ADDR_t;

// CONTROL1 register (Address: 0x0109, 8-bit, Default 0x00)
typedef struct stc_CONTROL1_field {
	uint8_t ADDR_WR         : 1;  // Bit [0]
	uint8_t SOFT_RESET      : 1;  // Bit [1]
	uint8_t GOTO_SLEEP      : 1;  // Bit [2]
	uint8_t GOTO_SHUTDOWN   : 1;  // Bit [3]
	uint8_t SEND_SLPTOACT   : 1;  // Bit [4]
	uint8_t SEND_WAKE       : 1;  // Bit [5]
	uint8_t SEND_SHUTDOWN   : 1;  // Bit [6]
	uint8_t DIR_SEL         : 1;  // Bit [7]
} stc_CONTROL1_field_t;

typedef union un_CONTROL1 {
    uint8_t u8Register;  // Default 0x00
    stc_CONTROL1_field_t stcField;
} un_CONTROL1_t;

#endif // SHQ8900_REGISTERS_HDR
