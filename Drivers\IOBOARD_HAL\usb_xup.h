#ifndef INC_USB_XUP_H_
#define INC_USB_XUP_H_

#define XUP_RX_DATA_SIZE 256
typedef struct tagXUPFIFO
{
  volatile uint32_t head;
  volatile uint32_t tail;
  int32_t  data[XUP_RX_DATA_SIZE];
} XUPFIFO;

#define XUP_FIFO_INCR(arg) ((arg) + 1)
#define XUP_FIFO_IDX(arg) ((arg) & ((XUP_RX_DATA_SIZE)-1))
//#define XUP_FIFO_INCR(x) (((x)+1) & ((XUP_RX_DATA_SIZE)-1))

//#ifndef MAX_XUP_CHANNELS
//#define MAX_XUP_CHANNELS 14
//#endif

//extern XUPFIFO XUP_RX_FIFO[MAX_XUP_CHANNELS]; // default 4 channels

void usb_process_xup_tasks();
void usb_set_xup_tasks(uint32_t tasks);
void usb_set_xup_batch(uint32_t batch_cnt);
void usb_set_xup_packaged_mode(uint32_t mode);

#endif /* INC_USB_XUP_H_ */
