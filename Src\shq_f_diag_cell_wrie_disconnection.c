#include <stdint.h>
#include <string.h>
#include "platform/comm_io.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

// 简化的诊断数据存储
static int32_t diag_before_volts[STACK_COUNT][MAX_CELLS_PER_AFE] = {0};
static int32_t diff_volts[STACK_COUNT][MAX_CELLS_PER_AFE] = {0};

/**
 * @brief 简化的堆栈电芯索引计算
 *
 * 计算指定堆栈索引的全局电芯起始索引
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @return 全局电芯起始索引
 */
static int32_t simple_stack_cell_index(SHQ_AFE_DATA *p_afe, int8_t stack_index)
{
    int32_t cell_index = 0;
    for (int8_t i = 0; i < stack_index; i++) {
        for (int8_t j = 0; j < p_afe->cell_counts[i]; j++) {
            if (j != p_afe->bus_bar_pos[i]) {
                cell_index++;
            }
        }
    }
    return cell_index;
}

/**
 * @brief 简化的电芯数值保存函数
 *
 * 保存指定堆栈的电芯ADC数值到数据结构中
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @param cell_index 全局电芯索引
 * @param pReadBuffer 读取缓冲区指针
 * @return 下一个全局电芯索引
 */
static int32_t simple_save_cs_values(SHQ_AFE_DATA *p_afe, int8_t stack_index, int32_t cell_index, uint8_t *pReadBuffer)
{
    int16_t code;
    int32_t volt_mv;

    for (int8_t j = 0; j < p_afe->cell_counts[stack_index]; j++) {
        // 处理VC电压 - 保存到诊断专用缓存区
        code = (pReadBuffer[j * 2] << 8) + pReadBuffer[j * 2 + 1];
        volt_mv = shq_code_to_mV(code);
        p_afe->diag_volts_vc[stack_index][j] = volt_mv;

        // 处理CB电压 - 保存到诊断专用缓存区
        code = (pReadBuffer[j * 2 + 36] << 8) + pReadBuffer[j * 2 + 36 + 1];
        volt_mv = shq_code_to_mV(code);
        p_afe->diag_volts_cb[stack_index][j] = volt_mv;

        // 跳过总线条位置
        if (j != p_afe->bus_bar_pos[stack_index]) {
            p_afe->indexed_vc_volts[cell_index] = p_afe->diag_volts_vc[stack_index][j];
            p_afe->indexed_cb_volts[cell_index] = p_afe->diag_volts_cb[stack_index][j];
            cell_index++;
        }
    }
    return cell_index;
}

/**
 * @brief 简化的电芯线路断线诊断函数
 *
 * 通过开线检测功能诊断电芯连接线路是否断开
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 诊断方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t diag_cell_wrie_disconnection(uintptr_t afe_ctx, int8_t dir)
{
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;
    uint8_t *pReadBuffer;
    int32_t ret;
    int8_t stack_count;
    un_ADC_CTRL6_t adc_ctrl6_val;
    int32_t cell_index;
    int8_t stack_index;

    print("inf: 开始电芯线路断线诊断\n");

    // 清除故障标志
    memset(p_afe->diag_afe_channels_fault, 0, sizeof(p_afe->diag_afe_channels_fault));

    // 1. 第一次正常采样 - 获取基准电压
    print("inf: 1. 正常采样获取基准电压\n");
    shq_set_adc_mode(DEV_ADDR_STACKS, false, 1, 0);
    shq_set_adc_start(DEV_ADDR_STACKS, 1, 0);
    simple_delay_ms(18); // 等待ADC转换完成
    // 获取堆栈数量
    stack_count = dir ? shq_get_bw_stack_count(p_afe) : shq_get_fw_stack_count(p_afe);
    if (stack_count < 1) {
        print("err: 没有找到堆栈设备\n");
        return COMME_STACK_ERROR;
    }

    // 读取基准电压数据
    ret = stack_read(R_FC1_HI, 72, stack_count, &pReadBuffer);
    if (ret <= 0) {
        print("err: 基准电压读取失败\n");
        return COMME_STACK_READ_ERROR;
    }

    // 处理基准数据
    if (dir) {
        pReadBuffer += DSC_FRAME_DATA_OFFSET;
        stack_index = p_afe->stack_count - p_afe->stack_count_bwd;
        cell_index = simple_stack_cell_index(p_afe, stack_index);
    } else {
        pReadBuffer += ((72 + DSC_RESP_NONE_DATA_BYTES) * (stack_count - 1) + DSC_FRAME_DATA_OFFSET);
        stack_index = 0;
        cell_index = 0;
    }

    // 保存基准电压数据
    for (int8_t s = 0; s < (dir ? p_afe->stack_count_bwd : p_afe->stack_count_fwd); s++) {
        cell_index = simple_save_cs_values(p_afe, stack_index, cell_index, pReadBuffer);
        if (dir) {
            pReadBuffer += (72 + DSC_RESP_NONE_DATA_BYTES);
        } else {
            pReadBuffer -= (72 + DSC_RESP_NONE_DATA_BYTES);
        }
        stack_index++;
    }

    // 保存基准CB电压到诊断专用缓存区
    for (int8_t i = 0; i < (dir ? p_afe->stack_count_bwd : p_afe->stack_count_fwd); i++) {
        for (int8_t c = 0; c < p_afe->cell_counts[i]; c++) {
            diag_before_volts[i][c] = p_afe->diag_volts_cb[i][c];
        }
    }
    // 2. 开线检测 - 奇数通道测试
    print("inf: 2. 开线检测 - 奇数通道测试\n");
    adc_ctrl6_val.u8Register = 0;
    adc_ctrl6_val.stcField.OWC_EN = 1; // 奇数通道开线检测
    shq_reg_write(DEV_ADDR_STACKS, R_ADC_CTRL6, adc_ctrl6_val.u8Register);

    shq_set_adc_mode(DEV_ADDR_STACKS, false, 1, 0);
    shq_set_adc_start(DEV_ADDR_STACKS, 1, 0);
    simple_delay_ms(18); // 等待ADC转换完成

    // 读取奇数通道开线检测结果
    ret = stack_read(R_FC1_HI, 72, stack_count, &pReadBuffer);
    if (ret <= 0) {
        print("err: 奇数通道开线检测读取失败\n");
        return COMME_STACK_READ_ERROR;
    }

    // 处理奇数通道数据
    if (dir) {
        pReadBuffer += DSC_FRAME_DATA_OFFSET;
        stack_index = p_afe->stack_count - p_afe->stack_count_bwd;
        cell_index = simple_stack_cell_index(p_afe, stack_index);
    } else {
        pReadBuffer += ((72 + DSC_RESP_NONE_DATA_BYTES) * (stack_count - 1) + DSC_FRAME_DATA_OFFSET);
        stack_index = 0;
        cell_index = 0;
    }

    // 保存奇数通道测试数据
    for (int8_t s = 0; s < (dir ? p_afe->stack_count_bwd : p_afe->stack_count_fwd); s++) {
        cell_index = simple_save_cs_values(p_afe, stack_index, cell_index, pReadBuffer);
        if (dir) {
            pReadBuffer += (72 + DSC_RESP_NONE_DATA_BYTES);
        } else {
            pReadBuffer -= (72 + DSC_RESP_NONE_DATA_BYTES);
        }
        stack_index++;
    }

    // 分析奇数通道差异
    print("inf: 分析奇数通道差异\n");
    // 安全检查：确保stack_count不超出范围
    int8_t safe_stack_count = (stack_count > STACK_COUNT) ? STACK_COUNT : stack_count;
    for (int8_t i = 0; i < safe_stack_count; i++) {
        for (int8_t j = 1; j < MAX_CELLS_PER_AFE; j += 2) { // 奇数通道
            // 安全检查：确保不会越界访问
            if (j < p_afe->cell_counts[i] && i < STACK_COUNT && j < MAX_CELLS_PER_AFE) {
                int32_t diff = ABS(diag_before_volts[i][j] - p_afe->diag_volts_cb[i][j]);
                diff_volts[i][j] = diff;
                print("inf: STACK %d CELL %d 差异 %d mV\n", i, j, diff);

                if (diff > LIMIT_CELL_OW_HIGH(diag_before_volts[i][j]) || diff < LIMIT_CELL_OW_LOW(diag_before_volts[i][j])) {
                    p_afe->diag_afe_channels_fault[i][j] = DIAG_FAULT_CELL_WRIE_DISCONN;
                    print("inf: 检测到奇数通道断线: STACK %d CELL %d\n", i, j);
                }
            }
        }
    }

    // 3. 开线检测 - 偶数通道测试
    print("inf: 3. 开线检测 - 偶数通道测试\n");
    adc_ctrl6_val.u8Register = 0;
    adc_ctrl6_val.stcField.OWC_EN = 2; // 偶数通道开线检测
    shq_reg_write(DEV_ADDR_STACKS, R_ADC_CTRL6, adc_ctrl6_val.u8Register);

    shq_set_adc_mode(DEV_ADDR_STACKS, false, 1, 0);
    shq_set_adc_start(DEV_ADDR_STACKS, 1, 0);
    simple_delay_ms(18); // 等待ADC转换完成

    // 读取偶数通道开线检测结果
    ret = stack_read(R_FC1_HI, 72, stack_count, &pReadBuffer);
    if (ret <= 0) {
        print("err: 偶数通道开线检测读取失败\n");
        return COMME_STACK_READ_ERROR;
    }

    // 处理偶数通道数据
    if (dir) {
        pReadBuffer += DSC_FRAME_DATA_OFFSET;
        stack_index = p_afe->stack_count - p_afe->stack_count_bwd;
        cell_index = simple_stack_cell_index(p_afe, stack_index);
    } else {
        pReadBuffer += ((72 + DSC_RESP_NONE_DATA_BYTES) * (stack_count - 1) + DSC_FRAME_DATA_OFFSET);
        stack_index = 0;
        cell_index = 0;
    }

    // 保存偶数通道测试数据
    for (int8_t s = 0; s < (dir ? p_afe->stack_count_bwd : p_afe->stack_count_fwd); s++) {
        cell_index = simple_save_cs_values(p_afe, stack_index, cell_index, pReadBuffer);
        if (dir) {
            pReadBuffer += (72 + DSC_RESP_NONE_DATA_BYTES);
        } else {
            pReadBuffer -= (72 + DSC_RESP_NONE_DATA_BYTES);
        }
        stack_index++;
    }

    // 分析偶数通道差异
    print("inf: 分析偶数通道差异\n");
    // 安全检查：确保stack_count不超出范围
    for (int8_t i = 0; i < safe_stack_count; i++) {
        for (int8_t j = 0; j < MAX_CELLS_PER_AFE; j += 2) { // 偶数通道
            // 安全检查：确保不会越界访问
            if (j < p_afe->cell_counts[i] && i < STACK_COUNT && j < MAX_CELLS_PER_AFE) {
                int32_t diff = ABS(diag_before_volts[i][j] - p_afe->diag_volts_cb[i][j]);
                diff_volts[i][j] = diff;
                print("inf: STACK %d CELL %d 差异 %d mV\n", i, j, diff);

                if (diff > LIMIT_CELL_OW_HIGH(diag_before_volts[i][j]) || diff < LIMIT_CELL_OW_LOW(diag_before_volts[i][j])) {
                    p_afe->diag_afe_channels_fault[i][j] = DIAG_FAULT_CELL_WRIE_DISCONN;
                    print("inf: 检测到偶数通道断线: STACK %d CELL %d\n", i, j);
                }
            }
        }
    }

    // 4. 最终分析 - 检测断线点
    print("inf: 4. 最终分析 - 检测断线点\n");
    for (int8_t i = 0; i < safe_stack_count; i++) {
        for (int8_t j = 0; j < MAX_CELLS_PER_AFE - 1; j++) {
            // 安全检查：确保不会越界访问
            if (j < p_afe->cell_counts[i] - 1 && i < STACK_COUNT && j < MAX_CELLS_PER_AFE - 1) {
                // 如果相邻两个通道都有大的差异，则是共用线断线
                if (p_afe -> diag_afe_channels_fault[i][j] == DIAG_FAULT_CELL_WRIE_DISCONN &&
                    p_afe -> diag_afe_channels_fault[i][j+1] == DIAG_FAULT_CELL_WRIE_DISCONN) {
                    print("inf: 检测到断线: STACK %d CELL %d\n", i, j+1);
                }
            }
        }
    }

    // 5. 关闭开线检测功能
    print("inf: 5. 关闭开线检测功能\n");
    adc_ctrl6_val.u8Register = 0;
    adc_ctrl6_val.stcField.OWC_EN = 0;
    shq_reg_write(DEV_ADDR_STACKS, R_ADC_CTRL6, adc_ctrl6_val.u8Register);

    print("inf: 电芯线路断线诊断完成\n");
    return 0;
}
