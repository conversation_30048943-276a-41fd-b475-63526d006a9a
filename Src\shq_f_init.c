#include <stdint.h>

#include "platform/comm_io.h"

#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"

#include "shq_afe.h"

/**
 * @brief 基础设备初始化
 *
 * 初始化AFE系统的基础/桥接设备
 *
 * @param p_afe AFE数据结构指针
 * @param dir 初始化方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
static int32_t simple_base_init(SHQ_AFE_DATA *p_afe, int8_t dir)
{
    // 简化版本：基础设备初始化
    (void)p_afe; // 暂时不使用
    (void)dir;   // 暂时不使用

    // 基础设备设置（可以根据需要添加具体配置）
    return 0;
}

/**
 * @brief 堆栈设备初始化
 *
 * 使用相同参数初始化AFE系统中的所有堆栈设备
 *
 * @param p_afe AFE数据结构指针
 * @param dir 初始化方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
static int32_t simple_stacks_init(SHQ_AFE_DATA *p_afe, int8_t dir)
{
    int32_t ret;
    int8_t dev_addr = DEV_ADDR_STACKS;
    un_ADC_DBG5_t adc_dbg5;
    un_ADC_CONF_t adc_conf;

    // 1. 设置ADC滤波器
    ret = shq_set_adc_filters(dev_addr, 0, 0, 128); // 16.9mS
    if (ret < 0) return ret;

    // 2. 设置ADC模式
    ret = shq_set_adc_mode(dev_addr, 0, 1, 0);
    if (ret < 0) return ret;

    // 3. 配置顶部/底部比较器
    adc_dbg5.u8Register = 0;
    adc_dbg5.stcField.SADC_EN_4_1 = 0x0f;
    adc_dbg5.stcField.FSC_SHIFT_top_en = 1;
    adc_dbg5.stcField.FSC_SHIFT_bot_en = 1;
    ret = shq_reg_write(dev_addr, R_ADC_DBG5, adc_dbg5.u8Register);
    if (ret < 0) return ret;

    // 4. 配置ADC设置
    adc_conf.u8Register = 0;
    adc_conf.stcField.FSC_SHIFT_bb_en = 1;
    adc_conf.stcField.STACK_DEV_NUM = (uint8_t)(dir ? p_afe->stack_count_bwd : p_afe->stack_count_fwd);
    ret = shq_reg_write(dev_addr, R_ADC_CONF, adc_conf.u8Register);

    return ret;
}

/**
 * @brief 单个堆栈初始化
 *
 * 初始化AFE系统中的特定堆栈设备
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引 (从0开始)
 * @param stack_addr 堆栈地址
 * @return 0表示成功，负数表示错误
 */
static int32_t simple_per_stack_init(SHQ_AFE_DATA *p_afe, int8_t stack_index, int8_t stack_addr)
{
    // 简化版本：每个堆栈的特殊配置
    (void)p_afe;       // 暂时不使用
    (void)stack_index; // 暂时不使用
    (void)stack_addr;  // 暂时不使用

    // 可以根据需要为每个堆栈添加特殊配置
    return 0;
}

/**
 * @brief AFE系统初始化函数
 *
 * 根据指定方向初始化AFE系统，包括基础初始化、堆栈初始化和每个堆栈的特殊配置
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 初始化方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_init(uintptr_t afe_ctx, int8_t dir)
{
    int32_t ret = 0;
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;

    // 1. 基础设备初始化
    ret = simple_base_init(p_afe, dir);
    if (ret < 0) {
        return ret;
    }

    // 2. 堆栈设备初始化
    ret = simple_stacks_init(p_afe, dir);
    if (ret < 0) {
        return ret;
    }

    // 3. 每个堆栈的特殊初始化
    if (dir) {
        // 反向初始化
        for (int8_t i = 0; i < p_afe->stack_count_bwd; i++) {
            ret = simple_per_stack_init(p_afe, p_afe->stack_count - 1 - i, i + 1);
            if (ret < 0) {
                return ret;
            }
        }
    } else {
        // 正向初始化
        for (int8_t stack_index = 0; stack_index < p_afe->stack_count_fwd; stack_index++) {
            ret = simple_per_stack_init(p_afe, stack_index, stack_index + 1);
            if (ret < 0) {
                return ret;
            }
        }
    }

    return ret;
}




