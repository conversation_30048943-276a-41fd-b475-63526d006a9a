#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq8900_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 基础设备唤醒函数
 *
 * 发送唤醒脉冲给基础/桥接设备，检查状态，设置方向为正向，设置看门狗定时器为2秒
 *
 * @return 0表示成功，负数表示错误
 */
int32_t shq_base_wakeup()
{
    un_COMM_TIMEOUT_t comm_timeout;

    // 1. 发送唤醒脉冲
    base_send_pulse(260);  // sleep to act, 250~300uS
    delay_us(100);
    base_send_pulse(4500); // shutdown to active, 4~5mS for 8900

    // 2. 等待设备激活
    simple_delay_ms(7);

    // 3. 检查设备是否就绪
    if (!dev_is_ready()) {
        // 清除通信
        dev_comm_clear();
        if (!dev_is_ready()) {
            print("inf:base error busy.\n");
            return COMME_BASE_ERROR;
        }
    }

    // 4. 设置默认方向为正向
    shq_set_base_dir_bit(0);

    // 5. 配置看门狗定时器
    comm_timeout.u8Register = 0;
#ifdef BASE_DIS_WDT
    comm_timeout.stcField.CTS_TIME = 0; // 禁用短超时
    comm_timeout.stcField.CTL_TIME = 0; // 禁用长超时
#else
    comm_timeout.stcField.CTL_ACT = 1;  // shutdown
    comm_timeout.stcField.CTL_TIME = 2; // 2秒
#endif
    single_dev_write(0, R_COMM_TIMEOUT, &comm_timeout.u8Register, 1); // 0号设备，基础设备
    delay_us(150);

    // 6. 最终检查设备状态
    if (!dev_is_ready()) { // 0号设备，基础设备
        dev_comm_clear(); // 0号设备，基础设备
        print("inf:base error busy.\n");
        return COMME_BASE_ERROR;
    }

    return 0;
}

