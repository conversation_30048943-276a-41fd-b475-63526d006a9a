#include <stdint.h>
#include <string.h>
#include "../platform/comm_io.h"
#include "crc.h"
#include "shq89xx.h"

static uint8_t trans_buffer[DSC_COMM_BUFFER_SIZE];

// 简化的调试输出函数
#ifdef PRINT_DSC_PACKAGES
static void simple_print_tx_package(int32_t frame_size)
{
    if (frame_size <= 0) return;
    print("inf:TX: ");
    for (int i = 0; i < frame_size; i++) {
        print(" %02X", trans_buffer[i]);
    }
    print("\n");
}

static void simple_print_rx_package(int32_t frame_size)
{
    if (frame_size <= 0) return;
    print("inf:RX: ");
    for (int i = 0; i < frame_size; i++) {
        print(" %02X", trans_buffer[i]);
    }
    print("\n");
}
#endif // PRINT_DSC_PACKAGES

/**
 * @brief Converts a code to millivolts (in 1mV units).
 * 
 * This function converts a given 16-bit code to its corresponding voltage in 1 millivolt (1mV) units.
 * The conversion formula used is: result = (code * 5000) / 32768.
 * 
 * @param code 16-bit signed integer representing the code to be converted.
 * 
 * @return The corresponding voltage in 1 millivolt (1mV) units.
 */
int32_t shq_code_to_mV(int16_t code)
{
	// res in 1mV
	// result = code*5000.0/32768.0
	int32_t val = (int32_t)code;
	int32_t mv = (val*5000 + (1<<14)) >> 15;
	return mv;
}

/**
 * @brief Converts a code to millivolts (in 0.1mV units).
 * 
 * This function converts a given 16-bit code to its corresponding voltage in 0.1 millivolt (0.1mV) units.
 * The conversion formula used is: result = (code * 50000) / 32768.
 * 
 * @param code 16-bit signed integer representing the code to be converted.
 * 
 * @return The corresponding voltage in 0.1 millivolt (0.1mV) units.
 */
int32_t shq_code_to_0p1mV(int16_t code)
{
	// res in 0.1mV
	// result = code*50000.0/32768.0
	int32_t val = (int32_t)code;
	int32_t mv = (val*25000 + (1<<13)) >> 14;
	return mv;
}

/**
 * @brief Converts a code to millivolts (in 0.01mV units).
 * 
 * This function converts a given 16-bit code to its corresponding voltage in 0.01 millivolt (0.01mV) units.
 * The conversion formula used is: result = (code * 500000) / 32768.
 * 
 * @param code 16-bit signed integer representing the code to be converted.
 * 
 * @return The corresponding voltage in 0.01 millivolt (0.01mV) units.
 */
int32_t shq_code_to_0p01mV(int16_t code)
{
	// res in 0.01mV
	// result = code*500000.0/32768.0
	int32_t val = (int32_t)code;
	int32_t mv = (val*62500 + (1<<11)) >> 12;
	return mv;
}

/**
 * @brief Converts a code to temperature (in 1℃ units).
 * 
 * This function converts a given 16-bit code to its corresponding voltage in 1℃ units.
 * The conversion formula used is: result = (code * 0.161) -274.
 * 
 * @param code 16-bit signed integer representing the code to be converted.
 * 
 * @return The corresponding voltage in 1 ℃ units.
 */
int32_t shq_code_to_temp(int16_t code)
{
	return (code*161)/1000 - 274;
}

/**
 * @brief 简化的通信请求包构建函数
 *
 * 根据指定的命令、设备地址、寄存器地址、数据和数据大小构建通信请求包
 *
 * @param trans_buffer 构建包的缓冲区指针
 * @param cmd 通信命令
 * @param devAddress 设备地址 (0为基础/桥接设备, 1-63为堆栈设备, <0为广播命令)
 * @param regAddress 寄存器地址 (16位)
 * @param data 要发送的数据指针 (写命令用)
 * @param dataSize 要发送的数据大小 (写命令用)
 * @return 构建包的总大小
 */
uint8_t build_request(uint8_t* trans_buffer, DSC_COMM_DEV_CMD cmd, int8_t devAddress, uint16_t regAddress, const uint8_t* data, uint8_t dataSize)
{
    // 清空缓冲区
    memset(trans_buffer, 0, DSC_COMM_BUFFER_SIZE);
    
    uint8_t idx = 0;
    uint16_t crc = 0;

    // 1. 构建命令字节
    if (cmd & 0x80) {
        // 写命令
        if (dataSize > 8) dataSize = 8; // 限制最大8字节
        trans_buffer[idx++] = 0x80 | ((cmd & 0x07) << 4) | ((dataSize - 1) & 0x07);
    } else {
        // 读命令
        trans_buffer[idx++] = 0x80 | ((cmd & 0x07) << 4);
    }

    // 2. 添加设备地址 (如果需要)
    if (devAddress >= 0) {
        trans_buffer[idx++] = devAddress & 0x3f;
    }

    // 3. 添加寄存器地址 (高字节在前)
    trans_buffer[idx++] = (uint8_t)(regAddress >> 8);
    trans_buffer[idx++] = (uint8_t)(regAddress & 0xff);

    // 4. 添加数据或数据长度
    if (cmd & 0x80) {
        // 写命令：添加实际数据
        for (int i = 0; i < dataSize; i++) {
            trans_buffer[idx++] = data[i];
        }
    } else {
        // 读命令：添加数据长度
        trans_buffer[idx++] = dataSize - 1;
    }

    // 5. 计算并添加CRC
    crc = calculate_crc(trans_buffer, idx);
    trans_buffer[idx++] = (uint8_t)(crc & 0xff);
    trans_buffer[idx++] = (uint8_t)(crc >> 8);

    // print("inf: build_request: len = %d, data = ", idx);
    // for(int i=0; i<idx; i++) {
    //     print("0x%x ", trans_buffer[i]);
    // }
    // print("\n");

    return idx;
}

/**
 * @brief 简化的响应帧检查函数
 *
 * 验证从设备接收的响应帧的完整性和格式正确性
 *
 * @param trans_buffer 包含响应帧的缓冲区指针
 * @param frameSize 响应帧大小
 * @param devAddress 发送响应的设备地址
 * @param regAddress 期望的寄存器地址
 * @return COMME_OK表示有效，负数表示各种错误
 */
int32_t check_response(uint8_t* trans_buffer, uint8_t frameSize, int8_t devAddress, uint16_t regAddress)
{
    // 1. CRC检查
    uint16_t crc = calculate_crc(trans_buffer, frameSize);
    if (crc != 0) {
        return COMME_CRC_FAILED;
    }

    // 2. 帧类型检查
    if (trans_buffer[0] & 0x80) {
        return COMME_FRAME_TYPE_UNKNOWN;
    }

    // 3. 数据大小检查
    if ((trans_buffer[0] & 0x7f) != (frameSize - DSC_RESP_NONE_DATA_BYTES - 1)) {
        return COMME_DATA_SIZE_ERROR;
    }

    // 4. 设备地址检查
    if ((devAddress > 0) && (devAddress != trans_buffer[1])) {
        return COMME_DEV_ADDR_UNKNOWN;
    }

    // 5. 寄存器地址检查
    if ((trans_buffer[2] != (regAddress >> 8)) || (trans_buffer[3] != (regAddress & 0xff))) {
        return COMME_REG_ADDR_UNKNOWN;
    }

    return COMME_OK;
}

/**
 * @brief Sends a pulse signal to the base device.
 * 
 * This function sends a pulse signal to the base device with the specified width.
 * 
 * @param width_us Width of the pulse signal in microseconds.
 * 
 * @retval >=0: Success.
 * @retval <0: Error occurred.
 */
int32_t base_send_pulse(uint32_t width_us)
{
	return dev_send_pulse(width_us);
}

/**
 * @brief 简化的单设备读取函数
 *
 * 从指定设备的寄存器地址读取数据
 *
 * @param devAddress 设备地址 (0为基础/桥接设备, 1-63为堆栈设备)
 * @param regAddress 寄存器地址 (16位)
 * @param numBytes 要读取的字节数
 * @param pRetBuf 存储读取数据的指针
 * @return >=0表示成功(返回读取的数据字节数), <0表示错误
 */
int32_t single_dev_read(int8_t devAddress, uint16_t regAddress, uint8_t numBytes, uint8_t **pRetBuf)
{
    // 1. 构建请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_SINGLE_READ, devAddress, regAddress, 0, numBytes);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送请求并接收响应
    uint8_t *p_ret_data = trans_buffer;
    frame_size = dev_spi_trans(trans_buffer, (uint32_t)frame_size, p_ret_data, DSC_RESP_NONE_DATA_BYTES + numBytes, 1, 100);

#ifdef PRINT_DSC_PACKAGES
    simple_print_rx_package(frame_size);
#endif

    // print("inf: single_dev_read: len = %d, data = ", frame_size);
    // for(int i=0; i<frame_size; i++) {
    //     print("0x%x ", p_ret_data[i]);
    // }
    // print("\n");

    // 3. 检查响应
    if (frame_size > 0) {
        int32_t ret = check_response(p_ret_data, (uint8_t)frame_size, devAddress, regAddress);
        if (ret == 0) {
            // 响应正确
            if (pRetBuf) {
                (*pRetBuf) = p_ret_data;
            }
            return p_ret_data[0] + 1; // 返回数据字节数
        }
        return ret; // 返回检查错误
    }

    return frame_size; // 返回传输错误
}

/**
 * @brief 简化的单设备写入函数
 *
 * 向指定设备的寄存器地址写入数据
 *
 * @param devAddress 设备地址 (0为基础/桥接设备, 1-63为堆栈设备)
 * @param regAddress 寄存器地址 (16位)
 * @param data 要写入的数据指针
 * @param dataSize 要写入的数据大小
 * @return >=0表示成功, <0表示错误
 */
int32_t single_dev_write(int8_t devAddress, uint16_t regAddress, const uint8_t* data, const uint8_t dataSize)
{
    // 1. 构建请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_SINGLE_WRITE, devAddress, regAddress, data, dataSize);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送请求 (写命令不需要接收响应)
    frame_size = dev_spi_trans(trans_buffer, (uint32_t)frame_size, 0, 0, 0, 100);

    return frame_size;
}

/**
 * @brief 简化的广播写入函数
 *
 * 向所有设备(包括堆栈/桥接/基础设备)广播写入数据
 *
 * @param regAddress 寄存器地址 (16位)
 * @param data 要写入的数据指针
 * @param dataSize 要写入的数据大小
 * @return >=0表示成功, <0表示错误
 */
int32_t broadcast_write(uint16_t regAddress, const uint8_t* data, uint8_t dataSize)
{
    // 1. 构建广播写入请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_BROADCAST_WRITE, NO_DEV_ADDRESS, regAddress, data, dataSize);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送广播请求 (广播命令不需要接收响应)
    frame_size = dev_spi_trans(trans_buffer, (uint32_t)frame_size, 0, 0, 0, 0);

    return frame_size;
}

/**
 * @brief 简化的反向广播写入函数
 *
 * 向所有设备以反向顺序广播写入数据
 *
 * @param regAddress 寄存器地址 (16位)
 * @param data 要写入的数据指针
 * @param dataSize 要写入的数据大小
 * @return >=0表示成功, <0表示错误
 */
int32_t broadcast_write_reverse(uint16_t regAddress, const uint8_t* data, uint8_t dataSize)
{
    // 1. 构建反向广播写入请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_BROADCAST_WRITE_REVERSE, NO_DEV_ADDRESS, regAddress, data, dataSize);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送反向广播请求
    frame_size = dev_spi_trans(trans_buffer, (uint32_t)frame_size, 0, 0, 0, 0);

    return frame_size;
}

/**
 * @brief 简化的堆栈设备写入函数
 *
 * 向所有堆栈设备写入数据
 *
 * @param regAddress 寄存器地址 (16位)
 * @param data 要写入的数据指针
 * @param dataSize 要写入的数据大小
 * @return >=0表示成功, <0表示错误
 */
int32_t stack_write(uint16_t regAddress, const uint8_t* data, uint8_t dataSize)
{
    // 1. 构建堆栈写入请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_STACK_WRITE, NO_DEV_ADDRESS, regAddress, data, dataSize);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送堆栈写入请求
    frame_size = dev_spi_trans(trans_buffer, (uint32_t)frame_size, 0, 0, 0, 0);

    return frame_size;
}

/**
 * @brief 简化的堆栈设备读取函数
 *
 * 从所有堆栈设备读取数据
 *
 * @param regAddress 寄存器地址 (16位)
 * @param numBytes 要读取的字节数
 * @param devCount 堆栈设备数量
 * @param pRetBuf 存储读取数据的指针
 * @return >=0表示成功, <0表示错误
 */
int32_t stack_read(uint16_t regAddress, uint8_t numBytes, uint8_t devCount, uint8_t **pRetBuf)
{
    // 1. 构建堆栈读取请求包
    int32_t frame_size = build_request(trans_buffer, COMMC_STACK_READ, NO_DEV_ADDRESS, regAddress, 0, numBytes);

#ifdef PRINT_DSC_PACKAGES
    simple_print_tx_package(frame_size);
#endif

    // 2. 发送请求并接收响应
    uint8_t *p_ret_data = trans_buffer;
    frame_size = dev_spi_trans(trans_buffer, frame_size,
                                      p_ret_data, DSC_RESP_NONE_DATA_BYTES + numBytes, devCount, 0);

#ifdef PRINT_DSC_PACKAGES
    simple_print_rx_package(frame_size);
#endif

    // 3. 返回数据指针
    if (frame_size > 0) {
        if (pRetBuf) {
            (*pRetBuf) = p_ret_data;
        }
    }

    return frame_size;
}
