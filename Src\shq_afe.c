#include <stdio.h>
#include <string.h>
#include <stdint.h>
#include <stdbool.h>

#include "platform/comm_io.h"

#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"

#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief Gets the forward stack count for the AFE device.
 * 
 * This function returns the forward stack count for the AFE device.
 * If there is no error in the forward stack (`stack_err_addr_fwd` is less than or equal to 0),
 * it returns the `stack_count_fwd` (designed). Otherwise, it returns `stack_err_addr_fwd - 1` (actual).
 * 
 * @param p_afe Pointer to the SHQ_AFE_DATA structure containing the AFE device data.
 * 
 * @return The forward stack count.
 */
int8_t shq_get_fw_stack_count(SHQ_AFE_DATA *p_afe)
{
	if (p_afe->stack_err_addr_fwd <= 0) {
		return p_afe->stack_count_fwd;
	}
	return p_afe->stack_err_addr_fwd - 1;
}

/**
 * @brief Gets the backward stack count for the AFE device.
 * 
 * This function returns the backward stack count for the AFE device.
 * If there is no error in the backward stack (`stack_err_addr_bwd` is less than or equal to 0),
 * it returns the `stack_count_bwd` (designed). Otherwise, it returns `stack_err_addr_bwd - 1` (actual).
 * 
 * @param p_afe Pointer to the SHQ_AFE_DATA structure containing the AFE device data.
 * 
 * @return The backward stack count.
 */
int8_t shq_get_bw_stack_count(SHQ_AFE_DATA *p_afe)
{
	if (p_afe->stack_err_addr_bwd <= 0) {
		return p_afe->stack_count_bwd;
	}
	return p_afe->stack_err_addr_bwd - 1;
}

/**
 * @brief Change the communication direction only.
 * 
 * This function changes the communication direction without redo addressing process.
 * It is used when the Daisy-chain is broken and re-initialized in both directions.
 * 
 * @param p_afe Pointer to the SHQ_AFE_DATA structure containing the AFE device data.
 * @param dir Direction to set (0 for forward, 1 for backward).
 * 
 * @return 0 on success.
 */
int32_t shq_re_dir(SHQ_AFE_DATA *p_afe, int8_t dir)
{
	uint8_t stack_count;
	un_COMM_CTRL_t comm_ctrl;

	shq_set_base_dir_bit(dir); // dir: 0为正向，1为反向

	comm_ctrl.u8Register = 0x00;
	comm_ctrl.stcField.STACK_DEV = 1;
	if (dir) {
		stack_count = p_afe->stack_count_bwd;
		// clear top
		broadcast_write_reverse(R_COMM_CTRL, &comm_ctrl.u8Register, 1);
	} else {
		stack_count = p_afe->stack_count_fwd;
		// clear top
		broadcast_write(R_COMM_CTRL, &comm_ctrl.u8Register, 1);
	}

	// set top
	comm_ctrl.u8Register = 0x00;
	comm_ctrl.stcField.TOP_STACK = 1;
	single_dev_write(stack_count, R_COMM_CTRL, &comm_ctrl.u8Register, 1);

	return 0;
}

/**
 * @brief Sets the base/bridge's direction bit to change the base/bridge's Daisy-chain communication direction.
 * 
 * This function sets the direction selection bit (`DIR_SEL`) in the `CONTROL1` register of the base/bridge device.
 * It waits for a short delay to ensure the change takes effect.
 * 
 * @param dir Direction to set (0 for forward, 1 for backward).
 * 
 * @return 0 on success, or a negative error code.
 */
int32_t shq_set_base_dir_bit(int8_t dir)
{
	un_CONTROL1_t control1;
	control1.u8Register = 0x00;
	control1.stcField.DIR_SEL = dir ? 1 : 0;
	if (dir) {
		broadcast_write_reverse(R_CONTROL1, &control1.u8Register, 1);
	} else {
		broadcast_write(R_CONTROL1, &control1.u8Register, 1);
	}
//	print("inf:2\n");
	delay_us(110);
	return 0;
}

/**
 * @brief Sets the base/bridge's and stacks' direction bit to change all devices Daisy-chain communication direction.
 * 
 * This function sets the direction selection bit (`DIR_SEL`) in the `CONTROL1` register of all devices in current Daisy-chain.
 * It waits for a short delay to ensure the change takes effect.
 * 
 * @param dir Direction to set (0 for forward, 1 for backward).
 * 
 * @return 0 on success, or a negative error code.
 */
int32_t shq_set_dir_bit(int8_t dir)
{

	un_CONTROL1_t control1;
	control1.u8Register = 0;
	control1.stcField.DIR_SEL = dir ? 1 : 0;
	if (dir) {
		broadcast_write_reverse(R_CONTROL1, &control1.u8Register, 1);
	} else {
		broadcast_write(R_CONTROL1, &control1.u8Register, 1);
	}
	delay_us(220);
	print("inf:1\n");
	return 0;
}
