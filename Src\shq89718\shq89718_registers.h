#ifndef SHQ89718_REGISTERS_HDR
#define SHQ89718_REGISTERS_HDR

#include <stdint.h>

#define R_PART_ID      0x000  // default 0x09, R
#define R_COMM_CONF    0x001  // default 0x00, R/W
#define R_COMM_STAT    0x002  // default 0x00, R-1C
#define R_I2C_WR_DATA  0x003  // default 0x00, R/W
#define R_I2C_CTRL     0x004  // default 0x00, R/W-AC
#define R_I2C_RD_DATA  0x005  // default 0x00, R
#define R_SPI_CONF     0x006  // default 0x00, R/W
#define R_SPI_TX3      0x007  // default 0x00, R/W
#define R_SPI_TX2      0x008  // default 0x00, R/W
#define R_SPI_TX1      0x009  // default 0x00, R/W
#define R_SPI_EXE      0x00A  // default 0x02, R/W-AC
#define R_SPI_RX3      0x00B  // default 0x00, R
#define R_SPI_RX2      0x00C  // default 0x00, R
#define R_SPI_RX1      0x00D  // default 0x00, R
#define R_CONTROL2     0x00E  // default 0x00, R/W
#define R_DAISY_CTL1   0x00F  // default 0x01, R/W
#define R_DAISY_CTL2   0x010  // default 0x22, R/W
#define R_BIST_GO      0x012  // default 0x00, R/W-AC
#define R_BIST_STAT    0x013  // default 0x00, R
#define R_SPARE_REG    0x014  // default 0x00, R/W
#define R_CB_CELL18_CTRL 0x018  // default 0x00, R/W
#define R_CB_CELL17_CTRL 0x019  // default 0x00, R/W
#define R_CB_CELL16_CTRL 0x01A  // default 0x00, R/W
#define R_CB_CELL15_CTRL 0x01B  // default 0x00, R/W
#define R_CB_CELL14_CTRL 0x01C  // default 0x00, R/W
#define R_CB_CELL13_CTRL 0x01D  // default 0x00, R/W
#define R_CB_CELL12_CTRL 0x01E  // default 0x00, R/W
#define R_CB_CELL11_CTRL 0x01F  // default 0x00, R/W
#define R_CB_CELL10_CTRL 0x020  // default 0x00, R/W
#define R_CB_CELL9_CTRL  0x021  // default 0x00, R/W
#define R_CB_CELL8_CTRL  0x022  // default 0x00, R/W
#define R_CB_CELL7_CTRL  0x023  // default 0x00, R/W
#define R_CB_CELL6_CTRL  0x024  // default 0x00, R/W
#define R_CB_CELL5_CTRL  0x025  // default 0x00, R/W
#define R_CB_CELL4_CTRL  0x026  // default 0x00, R/W
#define R_CB_CELL3_CTRL  0x027  // default 0x00, R/W
#define R_CB_CELL2_CTRL  0x028  // default 0x00, R/W
#define R_CB_CELL1_CTRL  0x029  // default 0x00, R/W
#define R_BAL_CTRL1    0x02A  // default 0x00, R/W
#define R_BAL_CTRL2    0x02B  // default 0x00, R/W
#define R_BAL_CTRL3    0x02C  // default 0x00, R/W
#define R_BAL_STAT     0x02D  // default 0x00, R
#define R_BAL_DONE1    0x02E  // default 0x00, R
#define R_BAL_DONE2    0x02F  // default 0x00, R
#define R_BAL_DONE3    0x030  // default 0x00, R
#define R_BAL_TIME     0x031  // default 0x00, R
#define R_ADC_CTRL1    0x032  // default 0x08, R/W
#define R_ADC_CTRL2    0x033  // default 0x00, R/W
#define R_ADC_CTRL3    0x034  // default 0x00, R/W
#define R_ADC_CTRL4    0x035  // default 0x00, R/W
#define R_ADC_CTRL5    0x036  // default 0x00, R/W-AC
#define R_ADC_CTRL6    0x037  // default 0x00, R/W
#define R_ADC_DBG1     0x038  // default 0xFF, R/W
#define R_ADC_DBG2     0x039  // default 0xFF, R/W
#define R_ADC_DBG3     0x03A  // default 0xFF, R/W
#define R_ADC_DBG4     0x03B  // default 0xFF, R/W
#define R_ADC_DBG5     0x03C  // default 0xF0, R/W
#define R_ADC_STAT     0x03D  // default 0x00, R
#define R_ADC_CONF     0x03E  // default 0x00, R/W
#define R_SHUTDOWN_STAT    0x03F  // default 0x80, R/W-1C
#define R_BB_CONF      0x040  // default 0x00, R/W
#define R_FSC_SCALE    0x041  // default 0x40, R/W
#define R_C1V_HI       0x042  // default 0x00, R
#define R_C1V_LO       0x043  // default 0x00, R
#define R_C2V_HI       0x044  // default 0x00, R
#define R_C2V_LO       0x045  // default 0x00, R
#define R_C3V_HI       0x046  // default 0x00, R
#define R_C3V_LO       0x047  // default 0x00, R
#define R_C4V_HI       0x048  // default 0x00, R
#define R_C4V_LO       0x049  // default 0x00, R
#define R_C5V_HI       0x04A  // default 0x00, R
#define R_C5V_LO       0x04B  // default 0x00, R
#define R_C6V_HI       0x04C  // default 0x00, R
#define R_C6V_LO       0x04D  // default 0x00, R
#define R_C7V_HI       0x04E  // default 0x00, R
#define R_C7V_LO       0x04F  // default 0x00, R
#define R_C8V_HI       0x050  // default 0x00, R
#define R_C8V_LO       0x051  // default 0x00, R
#define R_C9V_HI       0x052  // default 0x00, R
#define R_C9V_LO       0x053  // default 0x00, R
#define R_C10V_HI      0x054  // default 0x00, R
#define R_C10V_LO      0x055  // default 0x00, R
#define R_C11V_HI      0x056  // default 0x00, R
#define R_C11V_LO      0x057  // default 0x00, R
#define R_C12V_HI      0x058  // default 0x00, R
#define R_C12V_LO      0x059  // default 0x00, R
#define R_C13V_HI      0x05A  // default 0x00, R
#define R_C13V_LO      0x05B  // default 0x00, R
#define R_C14V_HI      0x05C  // default 0x00, R
#define R_C14V_LO      0x05D  // default 0x00, R
#define R_C15V_HI      0x05E  // default 0x00, R
#define R_C15V_LO      0x05F  // default 0x00, R
#define R_C16V_HI      0x060  // default 0x00, R
#define R_C16V_LO      0x061  // default 0x00, R
#define R_C17V_HI      0x062  // default 0x00, R
#define R_C17V_LO      0x063  // default 0x00, R
#define R_C18V_HI      0x064  // default 0x00, R
#define R_C18V_LO      0x065  // default 0x00, R
#define R_FC1_HI       0x066  // default 0x00, R
#define R_FC1_LO       0x067  // default 0x00, R
#define R_FC2_HI       0x068  // default 0x00, R
#define R_FC2_LO       0x069  // default 0x00, R
#define R_FC3_HI       0x06A  // default 0x00, R
#define R_FC3_LO       0x06B  // default 0x00, R
#define R_FC4_HI       0x06C  // default 0x00, R
#define R_FC4_LO       0x06D  // default 0x00, R
#define R_FC5_HI       0x06E  // default 0x00, R
#define R_FC5_LO       0x06F  // default 0x00, R
#define R_FC6_HI       0x070  // default 0x00, R
#define R_FC6_LO       0x071  // default 0x00, R
#define R_FC7_HI       0x072  // default 0x00, R
#define R_FC7_LO       0x073  // default 0x00, R
#define R_FC8_HI       0x074  // default 0x00, R
#define R_FC8_LO       0x075  // default 0x00, R
#define R_FC9_HI       0x076  // default 0x00, R
#define R_FC9_LO       0x077  // default 0x00, R
#define R_FC10_HI      0x078  // default 0x00, R
#define R_FC10_LO      0x079  // default 0x00, R
#define R_FC11_HI      0x07A  // default 0x00, R
#define R_FC11_LO      0x07B  // default 0x00, R
#define R_FC12_HI      0x07C  // default 0x00, R
#define R_FC12_LO      0x07D  // default 0x00, R
#define R_FC13_HI      0x07E  // default 0x00, R
#define R_FC13_LO      0x07F  // default 0x00, R
#define R_FC14_HI      0x080  // default 0x00, R
#define R_FC14_LO      0x081  // default 0x00, R
#define R_FC15_HI      0x082  // default 0x00, R
#define R_FC15_LO      0x083  // default 0x00, R
#define R_FC16_HI      0x084  // default 0x00, R
#define R_FC16_LO      0x085  // default 0x00, R
#define R_FC17_HI      0x086  // default 0x00, R
#define R_FC17_LO      0x087  // default 0x00, R
#define R_FC18_HI      0x088  // default 0x00, R
#define R_FC18_LO      0x089  // default 0x00, R
#define R_S1V_HI       0x08A  // default 0x00, R
#define R_S1V_LO       0x08B  // default 0x00, R
#define R_S2V_HI       0x08C  // default 0x00, R
#define R_S2V_LO       0x08D  // default 0x00, R
#define R_S3V_HI       0x08E  // default 0x00, R
#define R_S3V_LO       0x08F  // default 0x00, R
#define R_S4V_HI       0x090  // default 0x00, R
#define R_S4V_LO       0x091  // default 0x00, R
#define R_S5V_HI       0x092  // default 0x00, R
#define R_S5V_LO       0x093  // default 0x00, R
#define R_S6V_HI       0x094  // default 0x00, R
#define R_S6V_LO       0x095  // default 0x00, R
#define R_S7V_HI       0x096  // default 0x00, R
#define R_S7V_LO       0x097  // default 0x00, R
#define R_S8V_HI       0x098  // default 0x00, R
#define R_S8V_LO       0x099  // default 0x00, R
#define R_S9V_HI       0x09A  // default 0x00, R
#define R_S9V_LO       0x09B  // default 0x00, R
#define R_S10V_HI      0x09C  // default 0x00, R
#define R_S10V_LO      0x09D  // default 0x00, R
#define R_S11V_HI      0x09E  // default 0x00, R
#define R_S11V_LO      0x09F  // default 0x00, R
#define R_S12V_HI      0x0A0  // default 0x00, R
#define R_S12V_LO      0x0A1  // default 0x00, R
#define R_S13V_HI      0x0A2  // default 0x00, R
#define R_S13V_LO      0x0A3  // default 0x00, R
#define R_S14V_HI      0x0A4  // default 0x00, R
#define R_S14V_LO      0x0A5  // default 0x00, R
#define R_S15V_HI      0x0A6  // default 0x00, R
#define R_S15V_LO      0x0A7  // default 0x00, R
#define R_S16V_HI      0x0A8  // default 0x00, R
#define R_S16V_LO      0x0A9  // default 0x00, R
#define R_S17V_HI      0x0AA  // default 0x00, R
#define R_S17V_LO      0x0AB  // default 0x00, R
#define R_S18V_HI      0x0AC  // default 0x00, R
#define R_S18V_LO      0x0AD  // default 0x00, R
#define R_GP1V_HI      0x0AE  // default 0x00, R
#define R_GP1V_LO      0x0AF  // default 0x00, R
#define R_GP2V_HI      0x0B0  // default 0x00, R
#define R_GP2V_LO      0x0B1  // default 0x00, R
#define R_GP3V_HI      0x0B2  // default 0x00, R
#define R_GP3V_LO      0x0B3  // default 0x00, R
#define R_GP4V_HI      0x0B4  // default 0x00, R
#define R_GP4V_LO      0x0B5  // default 0x00, R
#define R_GP5V_HI      0x0B6  // default 0x00, R
#define R_GP5V_LO      0x0B7  // default 0x00, R
#define R_GP6V_HI      0x0B8  // default 0x00, R
#define R_GP6V_LO      0x0B9  // default 0x00, R
#define R_GP7V_HI      0x0BA  // default 0x00, R
#define R_GP7V_LO      0x0BB  // default 0x00, R
#define R_GP8V_HI      0x0BC  // default 0x00, R
#define R_GP8V_LO      0x0BD  // default 0x00, R
#define R_GP9V_HI      0x0BE  // default 0x00, R
#define R_GP9V_LO      0x0BF  // default 0x00, R
#define R_GP10V_HI     0x0C0  // default 0x00, R
#define R_GP10V_LO     0x0C1  // default 0x00, R
#define R_GP11V_HI     0x0C2  // default 0x00, R
#define R_GP11V_LO     0x0C3  // default 0x00, R
#define R_RG1V_HI      0x0C4  // default 0x00, R
#define R_RG1V_LO      0x0C5  // default 0x00, R
#define R_RG2V_HI      0x0C6  // default 0x00, R
#define R_RG2V_LO      0x0C7  // default 0x00, R
#define R_RG3V_HI      0x0C8  // default 0x00, R
#define R_RG3V_LO      0x0C9  // default 0x00, R
#define R_RG4V_HI      0x0CA  // default 0x00, R
#define R_RG4V_LO      0x0CB  // default 0x00, R
#define R_RG5V_HI      0x0CC  // default 0x00, R
#define R_RG5V_LO      0x0CD  // default 0x00, R
#define R_RG6V_HI      0x0CE  // default 0x00, R
#define R_RG6V_LO      0x0CF  // default 0x00, R
#define R_RG7V_HI      0x0D0  // default 0x00, R
#define R_RG7V_LO      0x0D1  // default 0x00, R
#define R_RG8V_HI      0x0D2  // default 0x00, R
#define R_RG8V_LO      0x0D3  // default 0x00, R
#define R_RG9V_HI      0x0D4  // default 0x00, R
#define R_RG9V_LO      0x0D5  // default 0x00, R
#define R_RG10V_HI     0x0D6  // default 0x00, R
#define R_RG10V_LO     0x0D7  // default 0x00, R
#define R_RG11V_HI     0x0D8  // default 0x00, R
#define R_RG11V_LO     0x0D9  // default 0x00, R
#define R_VRES_HI      0x0DA  // default 0x00, R
#define R_VRES_LO      0x0DB  // default 0x00, R
#define R_VREF2_HI     0x0DC  // default 0x00, R
#define R_VREF2_LO     0x0DD  // default 0x00, R
#define R_DVDD_1P8_HI  0x0DE  // default 0x00, R
#define R_DVDD_1P8_LO  0x0DF  // default 0x00, R
#define R_VBAT_HI      0x0E0  // default 0x00, R
#define R_VBAT_LO      0x0E1  // default 0x00, R
#define R_AVDD_5V_HI   0x0E2  // default 0x00, R
#define R_AVDD_5V_LO   0x0E3  // default 0x00, R
#define R_DVDD_5V_HI   0x0E4  // default 0x00, R
#define R_DVDD_5V_LO   0x0E5  // default 0x00, R
#define R_TS1_HI       0x0E6  // default 0x00, R
#define R_TS1_LO       0x0E7  // default 0x00, R
#define R_TS2_HI       0x0E8  // default 0x00, R
#define R_TS2_LO       0x0E9  // default 0x00, R
#define R_TS3_HI       0x0EA  // default 0x00, R
#define R_TS3_LO       0x0EB  // default 0x00, R
#define R_TS4_HI       0x0EC  // default 0x00, R
#define R_TS4_LO       0x0ED  // default 0x00, R

#define R_VREF1_HI     0x0F0  // default 0x00, R
#define R_VREF1_LO     0x0F1  // default 0x00, R

#define R_DIAG_S1      0x0F6  // default 0x00, R
#define R_DIAG_S2      0x0F7  // default 0x00, R
#define R_DIAG_S3      0x0F8  // default 0x00, R
#define R_DIAG_S4      0x0F9  // default 0x00, R

#define R_DIAG_S12     0x101  // default 0x00, R
#define R_DIAG_S13     0x102  // default 0x00, R
#define R_FAULT_SUMMARY 0x103  // default 0x00, R
#define R_FUSE_CRC     0x104  // default 0x00, R
#define R_OSC_CHECK    0x105  // default 0x00, R
#define R_DIR0_ADDR    0x106  // default 0x00, R/W
#define R_DIR1_ADDR    0x107  // default 0x00, R/W
#define R_COMM_CTRL    0x108  // default 0x00, R/W
#define R_CONTROL1     0x109  // default 0x00, R/W-AC

#define R_GPIO_CTRL1   0x122  // default 0xFF, R/W
#define R_GPIO_CTRL2   0x123  // default 0x04, R/W
#define R_GPIO_CTRL3   0x124  // default 0x00, R/W
#define R_GPIO_CTRL4   0x125  // default 0x00, R/W
#define R_MASK_DIAG_S1  0x126  // default 0x00, R/W
#define R_MASK_DIAG_S2  0x127  // default 0x00, R/W
#define R_MASK_DIAG_S3  0x128  // default 0x00, R/W
#define R_MASK_DIAG_S4  0x129  // default 0x00, R/W

#define R_MASK_DIAG_S12 0x131  // default 0x00, R/W
#define R_MASK_DIAG_S13 0x132  // default 0x00, R/W
#define R_FAULT_MASK   0x133  // default 0x00, R/W
#define R_CLR_FLT      0x134  // default 0x00, R/W-AC
#define R_GPIO_STAT1   0x135  // default 0x00, R
#define R_GPIO_STAT2   0x136  // default 0x00, R

typedef struct stc_PART_ID_field {
    uint8_t rev_id: 3;  // bits 0 to 2
    uint8_t chip_id: 5;  // bits 3 to 7
} stc_PART_ID_field_t;

typedef union un_PART_ID {
    uint8_t u8Register;
    stc_PART_ID_field_t stcField;
} un_PART_ID_t;

typedef struct stc_COMM_CONF_field {
    uint8_t rsv_0_3: 4;  // bits 0 to 3
    uint8_t TWO_STOP_EN: 1;  // bits 4 to 4
    uint8_t WDT_DIS: 1;  // bits 5 to 5
    uint8_t MUST_WR_1: 1;  // bits 6 to 6
    uint8_t rsv_7_7: 1;  // bits 7 to 7
} stc_COMM_CONF_field_t;

typedef union un_COMM_CONF {
    uint8_t u8Register;
    stc_COMM_CONF_field_t stcField;
} un_COMM_CONF_t;

typedef struct stc_COMM_STAT_field {
    uint8_t FRAME_CRC_ERROR: 1;  // bits 0 to 0
    uint8_t UART_STOP_FAULT: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_COMM_STAT_field_t;

typedef union un_COMM_STAT {
    uint8_t u8Register;
    stc_COMM_STAT_field_t stcField;
} un_COMM_STAT_t;

typedef struct stc_I2C_WR_DATA_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_I2C_WR_DATA_field_t;

typedef union un_I2C_WR_DATA {
    uint8_t u8Register;
    stc_I2C_WR_DATA_field_t stcField;
} un_I2C_WR_DATA_t;

typedef struct stc_I2C_CTRL_field {
    uint8_t I2C_GO: 1;  // bits 0 to 0
    uint8_t NACK: 1;  // bits 1 to 1
    uint8_t STOP: 1;  // bits 2 to 2
    uint8_t START: 1;  // bits 3 to 3
    uint8_t RECEIVE: 1;  // bits 4 to 4
    uint8_t SEND: 1;  // bits 5 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_I2C_CTRL_field_t;

typedef union un_I2C_CTRL {
    uint8_t u8Register;
    stc_I2C_CTRL_field_t stcField;
} un_I2C_CTRL_t;

typedef struct stc_I2C_RD_DATA_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_I2C_RD_DATA_field_t;

typedef union un_I2C_RD_DATA {
    uint8_t u8Register;
    stc_I2C_RD_DATA_field_t stcField;
} un_I2C_RD_DATA_t;

typedef struct stc_SPI_CONF_field {
    uint8_t NUMBIT: 5;  // bits 0 to 4
    uint8_t CPHA: 1;  // bits 5 to 5
    uint8_t CPOL: 1;  // bits 6 to 6
    uint8_t rsv_7_7: 1;  // bits 7 to 7
} stc_SPI_CONF_field_t;

typedef union un_SPI_CONF {
    uint8_t u8Register;
    stc_SPI_CONF_field_t stcField;
} un_SPI_CONF_t;

typedef struct stc_SPI_TX3_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_TX3_field_t;

typedef union un_SPI_TX3 {
    uint8_t u8Register;
    stc_SPI_TX3_field_t stcField;
} un_SPI_TX3_t;

typedef struct stc_SPI_TX2_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_TX2_field_t;

typedef union un_SPI_TX2 {
    uint8_t u8Register;
    stc_SPI_TX2_field_t stcField;
} un_SPI_TX2_t;

typedef struct stc_SPI_TX1_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_TX1_field_t;

typedef union un_SPI_TX1 {
    uint8_t u8Register;
    stc_SPI_TX1_field_t stcField;
} un_SPI_TX1_t;

typedef struct stc_SPI_EXE_field {
    uint8_t SPI_GO: 1;  // bits 0 to 0
    uint8_t SS_CTRL: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_SPI_EXE_field_t;

typedef union un_SPI_EXE {
    uint8_t u8Register;
    stc_SPI_EXE_field_t stcField;
} un_SPI_EXE_t;

typedef struct stc_SPI_RX3_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_RX3_field_t;

typedef union un_SPI_RX3 {
    uint8_t u8Register;
    stc_SPI_RX3_field_t stcField;
} un_SPI_RX3_t;

typedef struct stc_SPI_RX2_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_RX2_field_t;

typedef union un_SPI_RX2 {
    uint8_t u8Register;
    stc_SPI_RX2_field_t stcField;
} un_SPI_RX2_t;

typedef struct stc_SPI_RX1_field {
    uint8_t DATA: 8;  // bits 0 to 7
} stc_SPI_RX1_field_t;

typedef union un_SPI_RX1 {
    uint8_t u8Register;
    stc_SPI_RX1_field_t stcField;
} un_SPI_RX1_t;

typedef struct stc_CONTROL2_field {
    uint8_t SPI_MST_EN: 1;  // bits 0 to 0
    uint8_t I2C_MST_EN: 1;  // bits 1 to 1
    uint8_t CLK_MON_EN: 1;  // bits 2 to 2
    uint8_t PTC_EN: 1;  // bits 3 to 3
    uint8_t SPI_LOOPBACK: 1;  // bits 4 to 4
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_CONTROL2_field_t;

typedef union un_CONTROL2 {
    uint8_t u8Register;
    stc_CONTROL2_field_t stcField;
} un_CONTROL2_t;

typedef struct stc_DAISY_CTL1_field {
    uint8_t daisy_tx_ib_adj: 2;  // bits 0 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_DAISY_CTL1_field_t;

typedef union un_DAISY_CTL1 {
    uint8_t u8Register;
    stc_DAISY_CTL1_field_t stcField;
} un_DAISY_CTL1_t;

typedef struct stc_DAISY_CTL2_field {
    uint8_t daisy_rx_th_adj_coml: 4;  // bits 0 to 3
    uint8_t daisy_rx_th_adj_comh: 4;  // bits 4 to 7
} stc_DAISY_CTL2_field_t;

typedef union un_DAISY_CTL2 {
    uint8_t u8Register;
    stc_DAISY_CTL2_field_t stcField;
} un_DAISY_CTL2_t;

typedef struct stc_BIST_GO_field {
    uint8_t PWRBIST_GO: 1;  // bits 0 to 0
    uint8_t COMPBIST_GO: 1;  // bits 1 to 1
    uint8_t STUCKBIST_GO: 1;  // bits 2 to 2
    uint8_t COMP_FLT_INJ: 1;  // bits 3 to 3
    uint8_t rsv_4_7: 4;  // bits 4 to 7
} stc_BIST_GO_field_t;

typedef union un_BIST_GO {
    uint8_t u8Register;
    stc_BIST_GO_field_t stcField;
} un_BIST_GO_t;

typedef struct stc_BIST_STAT_field {
    uint8_t PWRBIST_DONE: 1;  // bits 0 to 0
    uint8_t PWRBIST_FAIL: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_BIST_STAT_field_t;

typedef union un_BIST_STAT {
    uint8_t u8Register;
    stc_BIST_STAT_field_t stcField;
} un_BIST_STAT_t;

// for CB_CELL18_CTRL to CB_CELL1_CTRL
typedef struct stc_CB_CELL_CTRL_field {
    uint8_t TIME: 5;  // bits 0 to 4
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_CB_CELL_CTRL_field_t;

// for CB_CELL18_CTRL to CB_CELL1_CTRL
typedef union un_CB_CELL_CTRL {
    uint8_t u8Register;
    stc_CB_CELL_CTRL_field_t stcField;
} un_CB_CELL_CTRL_t;

typedef struct stc_BAL_CTRL1_field {
    uint8_t PWM: 6;  // bits 0 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_BAL_CTRL1_field_t;

typedef union un_BAL_CTRL1 {
    uint8_t u8Register;
    stc_BAL_CTRL1_field_t stcField;
} un_BAL_CTRL1_t;

typedef struct stc_BAL_CTRL2_field {
    uint8_t BAL_GO: 1;  // bits 0 to 0
    uint8_t AUTO_BAL: 1;  // bits 1 to 1
    uint8_t BAL_ACT: 1;  // bits 2 to 2
    uint8_t CB_PAUSE: 1;  // bits 3 to 3
    uint8_t rsv_4_7: 4;  // bits 4 to 7
} stc_BAL_CTRL2_field_t;

typedef union un_BAL_CTRL2 {
    uint8_t u8Register;
    stc_BAL_CTRL2_field_t stcField;
} un_BAL_CTRL2_t;

typedef struct stc_BAL_CTRL3_field {
    uint8_t BAL_TIME_GO: 1;  // bits 0 to 0
    uint8_t BAL_TIME_SEL: 5;  // bits 1 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_BAL_CTRL3_field_t;

typedef union un_BAL_CTRL3 {
    uint8_t u8Register;
    stc_BAL_CTRL3_field_t stcField;
} un_BAL_CTRL3_t;

typedef struct stc_BAL_STAT_field {
    uint8_t CB_DONE: 1;  // bits 0 to 0
    uint8_t CB_RUN: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_BAL_STAT_field_t;

typedef union un_BAL_STAT {
    uint8_t u8Register;
    stc_BAL_STAT_field_t stcField;
} un_BAL_STAT_t;

typedef struct stc_BAL_DONE1_field {
    uint8_t CELL18_17: 2;  // bits 1 to 0
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_BAL_DONE1_field_t;

typedef struct stc_BAL_DONE1_field_by_cell {
	uint8_t CELL17: 1;  // bits 0 to 0
	uint8_t CELL18: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_BAL_DONE1_field_by_cell_t;

typedef union un_BAL_DONE1 {
    uint8_t u8Register;
    stc_BAL_DONE1_field_t stcField;
    stc_BAL_DONE1_field_by_cell_t stcFieldByCell;
} un_BAL_DONE1_t;

typedef struct stc_BAL_DONE2_field {
	uint8_t CELL16_9;  // bits 7 to 0
} stc_BAL_DONE2_field_t;

typedef struct stc_BAL_DONE2_field_by_cell {
	uint8_t CELL9: 1;  // bits 0 to 0
	uint8_t CELL10: 1;  // bits 1 to 1
	uint8_t CELL11: 1;  // bits 2 to 2
	uint8_t CELL12: 1;  // bits 3 to 3
	uint8_t CELL13: 1;  // bits 4 to 4
	uint8_t CELL14: 1;  // bits 5 to 5
	uint8_t CELL15: 1;  // bits 6 to 6
	uint8_t CELL16: 1;  // bits 7 to 7
} stc_BAL_DONE2_field_by_cell_t;

typedef union un_BAL_DONE2 {
    uint8_t u8Register;
    stc_BAL_DONE2_field_t stcField;
    stc_BAL_DONE2_field_by_cell_t stcFieldByCell;
} un_BAL_DONE2_t;

typedef struct stc_BAL_DONE3_field {
	uint8_t CELL8_1;  // bits 7 to 0
} stc_BAL_DONE3_field_t;

typedef struct stc_BAL_DONE3_field_by_cell {
	uint8_t CELL1: 1;  // bits 0 to 0
	uint8_t CELL2: 1;  // bits 1 to 1
	uint8_t CELL3: 1;  // bits 2 to 2
	uint8_t CELL4: 1;  // bits 3 to 3
	uint8_t CELL5: 1;  // bits 4 to 4
	uint8_t CELL6: 1;  // bits 5 to 5
	uint8_t CELL7: 1;  // bits 6 to 6
	uint8_t CELL8: 1;  // bits 7 to 7
} stc_BAL_DONE3_field_by_cell_t;

typedef union un_BAL_DONE3 {
    uint8_t u8Register;
    stc_BAL_DONE3_field_t stcField;
    stc_BAL_DONE3_field_by_cell_t stcFieldByCell;
} un_BAL_DONE3_t;

typedef struct stc_BAL_TIME_field {
    uint8_t TIME: 7;  // bits 0 to 6
    uint8_t TIME_UNIT: 1;  // bits 7 to 7
} stc_BAL_TIME_field_t;

typedef union un_BAL_TIME {
    uint8_t u8Register;
    stc_BAL_TIME_field_t stcField;
} un_BAL_TIME_t;

typedef struct stc_ADC_CTRL1_field {
    uint8_t rsv_0_0: 1;  // reserved bits 0 to 0
    uint8_t LPF_VCELL: 3;  // bits 1 to 3
    uint8_t CS_DR: 1;  // bits 4 to 4
    uint8_t GP_DR: 1;  // bits 5 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_ADC_CTRL1_field_t;

typedef union un_ADC_CTRL1 {
    uint8_t u8Register;
    stc_ADC_CTRL1_field_t stcField;
} un_ADC_CTRL1_t;

typedef struct stc_ADC_CTRL2_field {
    uint8_t GPIO_MUX_SEL: 5;  // bits 0 to 4
    uint8_t CS_ADC_MODE: 2;  // bits 5 to 6
    uint8_t FREEZE_EN: 1;  // bits 7 to 7
} stc_ADC_CTRL2_field_t;

typedef union un_ADC_CTRL2 {
    uint8_t u8Register;
    stc_ADC_CTRL2_field_t stcField;
} un_ADC_CTRL2_t;

typedef struct stc_ADC_CTRL3_field {
    uint8_t CVS_THR: 5;  // bits 0 to 4
    uint8_t AUX_THR: 3;  // bits 5 to 7
} stc_ADC_CTRL3_field_t;

typedef union un_ADC_CTRL3 {
    uint8_t u8Register;
    stc_ADC_CTRL3_field_t stcField;
} un_ADC_CTRL3_t;

typedef struct stc_ADC_CTRL4_field {
    uint8_t rsv_0_7: 8;  // bits 0 to 7
} stc_ADC_CTRL4_field_t;

typedef union un_ADC_CTRL4 {
    uint8_t u8Register;
    stc_ADC_CTRL4_field_t stcField;
} un_ADC_CTRL4_t;

typedef struct stc_ADC_CTRL5_field {
    uint8_t GP_ADC_GO: 1;  // bits 0 to 0
    uint8_t CS_ADC_GO: 1;  // bits 1 to 1
    uint8_t CLR_ADC: 1;  // bits 2 to 2
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_ADC_CTRL5_field_t;

typedef union un_ADC_CTRL5 {
    uint8_t u8Register;
    stc_ADC_CTRL5_field_t stcField;
} un_ADC_CTRL5_t;

typedef struct stc_ADC_CTRL6_field {
    uint8_t OWC_EN: 2;  // bits 0 to 1
    uint8_t OWA_PUP: 1;  // bits 2 to 2
    uint8_t OWA_EN: 1;  // bits 3 to 3
    uint8_t SOAK_TIME: 3;  // bits 4 to 6
    uint8_t SOAKON: 1;  // bits 7 to 7
} stc_ADC_CTRL6_field_t;

typedef union un_ADC_CTRL6 {
    uint8_t u8Register;
    stc_ADC_CTRL6_field_t stcField;
} un_ADC_CTRL6_t;

typedef struct stc_ADC_DBG1_field {
	uint8_t CADC_EN_18_11;  // bits 7 to 0
} stc_ADC_DBG1_field_t;

typedef struct stc_ADC_DBG1_field_by_cell {
	uint8_t CADC11_EN: 1;  // bits 0 to 0
	uint8_t CADC12_EN: 1;  // bits 1 to 1
	uint8_t CADC13_EN: 1;  // bits 2 to 2
	uint8_t CADC14_EN: 1;  // bits 3 to 3
	uint8_t CADC15_EN: 1;  // bits 4 to 4
	uint8_t CADC16_EN: 1;  // bits 5 to 5
	uint8_t CADC17_EN: 1;  // bits 6 to 6
	uint8_t CADC18_EN: 1;  // bits 7 to 7
} stc_ADC_DBG1_field_by_cell_t;

typedef union un_ADC_DBG1 {
    uint8_t u8Register;
    stc_ADC_DBG1_field_t stcField;
    stc_ADC_DBG1_field_by_cell_t stcFieldByCell;
} un_ADC_DBG1_t;

typedef struct stc_ADC_DBG2_field {
	uint8_t CADC_EN_10_3;  // bits 7 to 0
} stc_ADC_DBG2_field_t;

typedef struct stc_ADC_DBG2_field_by_cell {
	uint8_t CADC3_EN: 1;  // bits 0 to 0
	uint8_t CADC4_EN: 1;  // bits 1 to 1
	uint8_t CADC5_EN: 1;  // bits 2 to 2
	uint8_t CADC6_EN: 1;  // bits 3 to 3
	uint8_t CADC7_EN: 1;  // bits 4 to 4
	uint8_t CADC8_EN: 1;  // bits 5 to 5
	uint8_t CADC9_EN: 1;  // bits 6 to 6
	uint8_t CADC10_EN: 1;  // bits 7 to 7
} stc_ADC_DBG2_field_by_cell_t;

typedef union un_ADC_DBG2 {
    uint8_t u8Register;
    stc_ADC_DBG2_field_t stcField;
    stc_ADC_DBG2_field_by_cell_t stcFieldByCell;
} un_ADC_DBG2_t;

typedef struct stc_ADC_DBG3_field {
	uint8_t SADC_EN_18_13: 6;  // bits 5 to 0
	uint8_t CADC_EN_2_1: 2;  // bits 7 to 6
} stc_ADC_DBG3_field_t;

typedef struct stc_ADC_DBG3_field_by_cell {
	uint8_t SADC13_EN: 1;  // bits 0 to 0
	uint8_t SADC14_EN: 1;  // bits 1 to 1
	uint8_t SADC15_EN: 1;  // bits 2 to 2
	uint8_t SADC16_EN: 1;  // bits 3 to 3
	uint8_t SADC17_EN: 1;  // bits 4 to 4
	uint8_t SADC18_EN: 1;  // bits 5 to 5
	uint8_t CADC1_EN: 1;  // bits 6 to 6
	uint8_t CADC2_EN: 1;  // bits 7 to 7
} stc_ADC_DBG3_field_by_cell_t;

typedef union un_ADC_DBG3 {
    uint8_t u8Register;
    stc_ADC_DBG3_field_t stcField;
    stc_ADC_DBG3_field_by_cell_t stcFieldByCell;
} un_ADC_DBG3_t;

typedef struct stc_ADC_DBG4_field {
	uint8_t SADC_EN_12_5;  // bits 7 to 0
} stc_ADC_DBG4_field_t;

typedef struct stc_ADC_DBG4_field_by_cell {
	uint8_t SADC5_EN: 1;  // bits 0 to 0
	uint8_t SADC6_EN: 1;  // bits 1 to 1
	uint8_t SADC7_EN: 1;  // bits 2 to 2
	uint8_t SADC8_EN: 1;  // bits 3 to 3
	uint8_t SADC9_EN: 1;  // bits 4 to 4
	uint8_t SADC10_EN: 1;  // bits 5 to 5
	uint8_t SADC11_EN: 1;  // bits 6 to 6
	uint8_t SADC12_EN: 1;  // bits 7 to 7
} stc_ADC_DBG4_field_by_cell_t;

typedef union un_ADC_DBG4 {
    uint8_t u8Register;
    stc_ADC_DBG4_field_t stcField;
    stc_ADC_DBG4_field_by_cell_t stcFieldByCell;
} un_ADC_DBG4_t;

typedef struct stc_ADC_DBG5_field {
    uint8_t DELTA_OFC_1_EN: 1;  // bits 0 to 0
    uint8_t DELTA_OFC_18_EN: 1;  // bits 1 to 1
    uint8_t FSC_SHIFT_top_en: 1;  // bits 2 to 2
    uint8_t FSC_SHIFT_bot_en: 1;  // bits 3 to 3
	uint8_t SADC_EN_4_1: 4; // bits 7 to 4
} stc_ADC_DBG5_field_t;

typedef struct stc_ADC_DBG5_field_by_cell {
    uint8_t DELTA_OFC_1_EN: 1;  // bits 0 to 0
    uint8_t DELTA_OFC_18_EN: 1;  // bits 1 to 1
    uint8_t FSC_SHIFT_top_en: 1;  // bits 2 to 2
    uint8_t FSC_SHIFT_bot_en: 1;  // bits 3 to 3
	uint8_t SADC1_EN: 1;  // bits 4 to 4
	uint8_t SADC2_EN: 1;  // bits 5 to 5
	uint8_t SADC3_EN: 1;  // bits 6 to 6
	uint8_t SADC4_EN: 1;  // bits 7 to 7
} stc_ADC_DBG5_field_by_cell_t;

typedef union un_ADC_DBG5 {
    uint8_t u8Register;
    stc_ADC_DBG5_field_t stcField;
    stc_ADC_DBG5_field_by_cell_t stcFieldByCell;
} un_ADC_DBG5_t;

typedef struct stc_ADC_STAT_field {
    uint8_t DRDY_AUX2: 1;  // bits 0 to 0
    uint8_t DRDY_AUX: 1;  // bits 1 to 1
    uint8_t DRDY_CADC: 1;  // bits 2 to 2
    uint8_t DRDY_SADC: 1;  // bits 3 to 3
    uint8_t DRDY_LPF: 1;  // bits 4 to 4
    uint8_t AUX_RUN: 1;  // bits 5 to 5
    uint8_t CSADC_RUN: 1;  // bits 6 to 6
    uint8_t FREEZE_ACTIVE: 1;  // bits 7 to 7
} stc_ADC_STAT_field_t;

typedef union un_ADC_STAT {
    uint8_t u8Register;
    stc_ADC_STAT_field_t stcField;
} un_ADC_STAT_t;

typedef struct stc_ADC_CONF_field {
    uint8_t STACK_DEV_NUM: 6;  // bits 0 to 5
    uint8_t no_cb_pause_adc_on: 1;  // bits 6 to 6
    uint8_t FSC_SHIFT_bb_en: 1;  // bits 7 to 7
} stc_ADC_CONF_field_t;

typedef union un_ADC_CONF {
    uint8_t u8Register;
    stc_ADC_CONF_field_t stcField;
} un_ADC_CONF_t;

typedef struct stc_SHUTDOWN_STAT_field {
    uint8_t rsv_0_6: 7;  // reserved bits 0 to 6
    uint8_t SHUT_DOWN_FLAG: 1;  // bits 7 to 7
} stc_SHUTDOWN_STAT_field_t;

typedef union un_SHUTDOWN_STAT {
    uint8_t u8Register;
    stc_SHUTDOWN_STAT_field_t stcField;
} un_SHUTDOWN_STAT_t;

typedef struct stc_BB_CONF_field {
    uint8_t rsv_0_0: 1;  // reserved bits 0 to 0
    uint8_t BB_THR: 2;  // bits 1 to 2
    uint8_t BB_CH: 5;  // bits 3 to 7
} stc_BB_CONF_field_t;

typedef union un_BB_CONF {
    uint8_t u8Register;
    stc_BB_CONF_field_t stcField;
} un_BB_CONF_t;

typedef struct stc_FSC_SCALE_field {
    uint8_t FSC_SCALE: 8;  // bits 0 to 7
} stc_FSC_SCALE_field_t;

typedef union un_FSC_SCALE {
    uint8_t u8Register;
    stc_FSC_SCALE_field_t stcField;
} un_FSC_SCALE_t;

typedef struct stc_DIAG_S1_field {
	uint8_t CVS_FLT_8_1; // bits 7 to 0
} stc_DIAG_S1_field_t;

typedef struct stc_DIAG_S1_field_by_channel {
	uint8_t CVS_FLT1: 1;  // bits 0 to 0
	uint8_t CVS_FLT2: 1;  // bits 1 to 1
	uint8_t CVS_FLT3: 1;  // bits 2 to 2
	uint8_t CVS_FLT4: 1;  // bits 3 to 3
	uint8_t CVS_FLT5: 1;  // bits 4 to 4
	uint8_t CVS_FLT6: 1;  // bits 5 to 5
	uint8_t CVS_FLT7: 1;  // bits 6 to 6
	uint8_t CVS_FLT8: 1;  // bits 7 to 7
} stc_DIAG_S1_field_by_channel_t;

typedef union un_DIAG_S1 {
    uint8_t u8Register;
    stc_DIAG_S1_field_t stcField;
    stc_DIAG_S1_field_by_channel_t stcFieldByChannel;
} un_DIAG_S1_t;

typedef struct stc_DIAG_S2_field {
	uint8_t CVS_FLT_16_9; // bits 7 to 0
} stc_DIAG_S2_field_t;

typedef struct stc_DIAG_S2_field_by_channel {
	uint8_t CVS_FLT9: 1;  // bits 0 to 0
	uint8_t CVS_FLT10: 1;  // bits 1 to 1
	uint8_t CVS_FLT11: 1;  // bits 2 to 2
	uint8_t CVS_FLT12: 1;  // bits 3 to 3
	uint8_t CVS_FLT13: 1;  // bits 4 to 4
	uint8_t CVS_FLT14: 1;  // bits 5 to 5
	uint8_t CVS_FLT15: 1;  // bits 6 to 6
	uint8_t CVS_FLT16: 1;  // bits 7 to 7
} stc_DIAG_S2_field_by_channel_t;

typedef union un_DIAG_S2 {
    uint8_t u8Register;
    stc_DIAG_S2_field_t stcField;
    stc_DIAG_S2_field_by_channel_t stcFieldByChannel;
} un_DIAG_S2_t;

typedef struct stc_DIAG_S3_field {
	uint8_t CVS_FLT_18_17: 2; // bits 1 to 0
	uint8_t AUX_FLT_11_9: 3; // bits 4 to 2
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_DIAG_S3_field_t;

typedef struct stc_DIAG_S3_field_by_channel {
	uint8_t CVS_FLT17: 1;  // bits 0 to 0
	uint8_t CVS_FLT18: 1;  // bits 1 to 1
	uint8_t AUX_FLT9: 1;  // bits 2 to 2
	uint8_t AUX_FLT10: 1;  // bits 3 to 3
	uint8_t AUX_FLT11: 1;  // bits 4 to 4
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_DIAG_S3_field_by_channel_t;

typedef union un_DIAG_S3 {
    uint8_t u8Register;
    stc_DIAG_S3_field_t stcField;
    stc_DIAG_S3_field_by_channel_t stcFieldByChannel;
} un_DIAG_S3_t;

typedef struct stc_DIAG_S4_field {
	uint8_t AUX_FLT_8_1; // bits 7 to 0
} stc_DIAG_S4_field_t;

typedef struct stc_DIAG_S4_field_by_channel {
	uint8_t AUX_FLT1: 1;  // bits 0 to 0
	uint8_t AUX_FLT2: 1;  // bits 1 to 1
	uint8_t AUX_FLT3: 1;  // bits 2 to 2
	uint8_t AUX_FLT4: 1;  // bits 3 to 3
	uint8_t AUX_FLT5: 1;  // bits 4 to 4
	uint8_t AUX_FLT6: 1;  // bits 5 to 5
	uint8_t AUX_FLT7: 1;  // bits 6 to 6
	uint8_t AUX_FLT8: 1;  // bits 7 to 7
} stc_DIAG_S4_field_by_channel_t;

typedef union un_DIAG_S4 {
    uint8_t u8Register;
    stc_DIAG_S4_field_t stcField;
    stc_DIAG_S4_field_by_channel_t stcFieldByChannel;
} un_DIAG_S4_t;


typedef struct stc_DIAG_S12_field {
    uint8_t rsv_0_5: 6;  // bits 0 to 5
    uint8_t CB_PFAIL: 1;  // bits 6 to 6
    uint8_t ADC_PFAIL: 1;  // bits 7 to 7
} stc_DIAG_S12_field_t;

typedef union un_DIAG_S12 {
    uint8_t u8Register;
    stc_DIAG_S12_field_t stcField;
} un_DIAG_S12_t;

typedef struct stc_DIAG_S13_field {
    uint8_t DVDD_UV: 1;  // bits 0 to 0
    uint8_t DVDD_OV: 1;  // bits 1 to 1
    uint8_t AVDD_UV: 1;  // bits 2 to 2
    uint8_t AVDD_OV: 1;  // bits 3 to 3
    uint8_t OSC_ERR: 1;  // bits 4 to 4
    uint8_t FUSE_CRC_ERR: 1;  // bits 5 to 5
    uint8_t GND_OW_FAIL: 1;  // bits 6 to 6
    uint8_t rsv_7_7: 1;  // bits 7 to 7
} stc_DIAG_S13_field_t;

typedef union un_DIAG_S13 {
    uint8_t u8Register;
    stc_DIAG_S13_field_t stcField;
} un_DIAG_S13_t;

typedef struct stc_FAULT_SUMMARY_field {
    uint8_t FAULT_SYS: 1;  // bits 0 to 0
    uint8_t rsv_1_2: 2;  // bits 1 to 2
    uint8_t FAULT_AUX_ADC: 1;  // bits 3 to 3
    uint8_t FAULT_CS_ADC: 1;  // bits 4 to 4
    uint8_t FAULT_PARITY: 1;  // bits 5 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_FAULT_SUMMARY_field_t;

typedef union un_FAULT_SUMMARY {
    uint8_t u8Register;
    stc_FAULT_SUMMARY_field_t stcField;
} un_FAULT_SUMMARY_t;

typedef struct stc_FUSE_CRC_field {
    uint8_t FUSE_CRC_COMPUTED: 8;  // bits 0 to 7
} stc_FUSE_CRC_field_t;

typedef union un_FUSE_CRC {
    uint8_t u8Register;
    stc_FUSE_CRC_field_t stcField;
} un_FUSE_CRC_t;

typedef struct stc_OSC_CHECK_field {
    uint8_t OC_COUNT: 8;  // bits 0 to 7
} stc_OSC_CHECK_field_t;

typedef union un_OSC_CHECK {
    uint8_t u8Register;
    stc_OSC_CHECK_field_t stcField;
} un_OSC_CHECK_t;

typedef struct stc_DIR0_ADDR_field {
    uint8_t ADDRESS: 6;  // bits 0 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_DIR0_ADDR_field_t;

typedef union un_DIR0_ADDR {
    uint8_t u8Register;
    stc_DIR0_ADDR_field_t stcField;
} un_DIR0_ADDR_t;

typedef struct stc_DIR1_ADDR_field {
    uint8_t ADDRESS: 6;  // bits 0 to 5
    uint8_t rsv_6_7: 2;  // bits 6 to 7
} stc_DIR1_ADDR_field_t;

typedef union un_DIR1_ADDR {
    uint8_t u8Register;
    stc_DIR1_ADDR_field_t stcField;
} un_DIR1_ADDR_t;

typedef struct stc_COMM_CTRL_field {
    uint8_t TOP_STACK: 1;  // bits 0 to 0
    uint8_t STACK_DEV: 1;  // bits 1 to 1
    uint8_t rsv_2_7: 6;  // bits 2 to 7
} stc_COMM_CTRL_field_t;

typedef union un_COMM_CTRL {
    uint8_t u8Register;
    stc_COMM_CTRL_field_t stcField;
} un_COMM_CTRL_t;

typedef struct stc_CONTROL1_field {
    uint8_t ADDR_WR: 1;  // bits 0 to 0
    uint8_t SOFT_RESET: 1;  // bits 1 to 1
    uint8_t GOTO_SLEEP: 1;  // bits 2 to 2
    uint8_t GOTO_SHUTDOWN: 1;  // bits 3 to 3
    uint8_t SEND_SLPTOACT: 1;  // bits 4 to 4
    uint8_t SEND_WAKE: 1;  // bits 5 to 5
    uint8_t SEND_SD_HW_RST: 1;  // bits 6 to 6
    uint8_t DIR_SEL: 1;  // bits 7 to 7
} stc_CONTROL1_field_t;

typedef union un_CONTROL1 {
    uint8_t u8Register;
    stc_CONTROL1_field_t stcField;
} un_CONTROL1_t;

typedef struct stc_GPIO_CTRL1_field {
	uint8_t IO_EN_ANA_8_1; // bits 7 to 0
} stc_GPIO_CTRL1_field_t;

typedef struct stc_GPIO_CTRL1_field_by_io {
	uint8_t IO1_EN_ANA: 1;  // bits 0 to 0
	uint8_t IO2_EN_ANA: 1;  // bits 1 to 1
	uint8_t IO3_EN_ANA: 1;  // bits 2 to 2
	uint8_t IO4_EN_ANA: 1;  // bits 3 to 3
	uint8_t IO5_EN_ANA: 1;  // bits 4 to 4
	uint8_t IO6_EN_ANA: 1;  // bits 5 to 5
	uint8_t IO7_EN_ANA: 1;  // bits 6 to 6
	uint8_t IO8_EN_ANA: 1;  // bits 7 to 7
} stc_GPIO_CTRL1_field_by_io_t;

typedef union un_GPIO_CTRL1 {
    uint8_t u8Register;
    stc_GPIO_CTRL1_field_t stcField;
    stc_GPIO_CTRL1_field_by_io_t stcFieldByIO;
} un_GPIO_CTRL1_t;

typedef struct stc_GPIO_CTRL2_field {
	uint8_t IO_EN_ANA_11_9: 3; // bits 2 to 0
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_CTRL2_field_t;

typedef struct stc_GPIO_CTRL2_field_by_io {
	uint8_t IO9_EN_ANA: 1;  // bits 0 to 0
	uint8_t IO10_EN_ANA: 1;  // bits 1 to 1
	uint8_t IO11_EN_ANA: 1;  // bits 2 to 2
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_CTRL2_field_by_io_t;

typedef union un_GPIO_CTRL2 {
    uint8_t u8Register;
    stc_GPIO_CTRL2_field_t stcField;
    stc_GPIO_CTRL2_field_by_io_t stcFieldByIO;
} un_GPIO_CTRL2_t;

typedef struct stc_GPIO_CTRL3_field {
	uint8_t IO_OD_DRV_8_1; // bits 7 to 0
} stc_GPIO_CTRL3_field_t;

typedef struct stc_GPIO_CTRL3_field_by_io {
	uint8_t IO1_OD_DRV: 1;  // bits 0 to 0
	uint8_t IO2_OD_DRV: 1;  // bits 1 to 1
	uint8_t IO3_OD_DRV: 1;  // bits 2 to 2
	uint8_t IO4_OD_DRV: 1;  // bits 3 to 3
	uint8_t IO5_OD_DRV: 1;  // bits 4 to 4
	uint8_t IO6_OD_DRV: 1;  // bits 5 to 5
	uint8_t IO7_OD_DRV: 1;  // bits 6 to 6
	uint8_t IO8_OD_DRV: 1;  // bits 7 to 7
} stc_GPIO_CTRL3_field_by_io_t;

typedef union un_GPIO_CTRL3 {
    uint8_t u8Register;
    stc_GPIO_CTRL3_field_t stcField;
    stc_GPIO_CTRL3_field_by_io_t stcFieldByIO;
} un_GPIO_CTRL3_t;

typedef struct stc_GPIO_CTRL4_field {
	uint8_t IO_OD_DRV_11_9: 3; // bits 2 to 0
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_CTRL4_field_t;

typedef struct stc_GPIO_CTRL4_field_by_io {
	uint8_t IO9_OD_DRV: 1;  // bits 0 to 0
	uint8_t IO10_OD_DRV: 1;  // bits 1 to 1
	uint8_t IO11_OD_DRV: 1;  // bits 2 to 2
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_CTRL4_field_by_io_t;

typedef union un_GPIO_CTRL4 {
    uint8_t u8Register;
    stc_GPIO_CTRL4_field_t stcField;
    stc_GPIO_CTRL4_field_by_io_t stcFieldByIO;
} un_GPIO_CTRL4_t;

typedef struct stc_MASK_DIAG_S1_field {
	uint8_t CVS_FLT_MASK_8_1; // bits 7 to 0
} stc_MASK_DIAG_S1_field_t;

typedef struct stc_MASK_DIAG_S1_field_by_channel {
	uint8_t CVS_FLT_MASK1: 1;  // bits 0 to 0
	uint8_t CVS_FLT_MASK2: 1;  // bits 1 to 1
	uint8_t CVS_FLT_MASK3: 1;  // bits 2 to 2
	uint8_t CVS_FLT_MASK4: 1;  // bits 3 to 3
	uint8_t CVS_FLT_MASK5: 1;  // bits 4 to 4
	uint8_t CVS_FLT_MASK6: 1;  // bits 5 to 5
	uint8_t CVS_FLT_MASK7: 1;  // bits 6 to 6
	uint8_t CVS_FLT_MASK8: 1;  // bits 7 to 7
} stc_MASK_DIAG_S1_field_by_channel_t;

typedef union un_MASK_DIAG_S1 {
    uint8_t u8Register;
    stc_MASK_DIAG_S1_field_t stcField;
    stc_MASK_DIAG_S1_field_by_channel_t stcFieldByChannel;
} un_MASK_DIAG_S1_t;

typedef struct stc_MASK_DIAG_S2_field {
	uint8_t CVS_FLT_MASK_16_9; // bits 7 to 0
} stc_MASK_DIAG_S2_field_t;

typedef struct stc_MASK_DIAG_S2_field_by_channel {
	uint8_t CVS_FLT_MASK9: 1;  // bits 0 to 0
	uint8_t CVS_FLT_MASK10: 1;  // bits 1 to 1
	uint8_t CVS_FLT_MASK11: 1;  // bits 2 to 2
	uint8_t CVS_FLT_MASK12: 1;  // bits 3 to 3
	uint8_t CVS_FLT_MASK13: 1;  // bits 4 to 4
	uint8_t CVS_FLT_MASK14: 1;  // bits 5 to 5
	uint8_t CVS_FLT_MASK15: 1;  // bits 6 to 6
	uint8_t CVS_FLT_MASK16: 1;  // bits 7 to 7
} stc_MASK_DIAG_S2_field_by_channel_t;

typedef union un_MASK_DIAG_S2 {
    uint8_t u8Register;
    stc_MASK_DIAG_S2_field_t stcField;
    stc_MASK_DIAG_S2_field_by_channel_t stcFieldByChannel;
} un_MASK_DIAG_S2_t;

typedef struct stc_MASK_DIAG_S3_field {
	uint8_t CVS_FLT_MASK_18_17: 2; // bits 1 to 0
	uint8_t AUX_FLT_MASK_11_9: 3; // bits 4 to 2
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_MASK_DIAG_S3_field_t;

typedef struct stc_MASK_DIAG_S3_field_by_channel {
	uint8_t CVS_FLT_MASK17: 1;  // bits 0 to 0
	uint8_t CVS_FLT_MASK18: 1;  // bits 1 to 1
	uint8_t AUX_FLT_MASK9: 1;  // bits 2 to 2
	uint8_t AUX_FLT_MASK10: 1;  // bits 3 to 3
	uint8_t AUX_FLT_MASK11: 1;  // bits 4 to 4
    uint8_t rsv_5_7: 3;  // bits 5 to 7
} stc_MASK_DIAG_S3_field_by_channel_t;

typedef union un_MASK_DIAG_S3 {
    uint8_t u8Register;
    stc_MASK_DIAG_S3_field_t stcField;
    stc_MASK_DIAG_S3_field_by_channel_t stcFieldByChannel;
} un_MASK_DIAG_S3_t;

typedef struct stc_MASK_DIAG_S4_field {
	uint8_t AUX_FLT_MASK_8_1; // bits 7 to 0
} stc_MASK_DIAG_S4_field_t;

typedef struct stc_MASK_DIAG_S4_field_by_channel {
	uint8_t AUX_FLT_MASK1: 1;  // bits 0 to 0
	uint8_t AUX_FLT_MASK2: 1;  // bits 1 to 1
	uint8_t AUX_FLT_MASK3: 1;  // bits 2 to 2
	uint8_t AUX_FLT_MASK4: 1;  // bits 3 to 3
	uint8_t AUX_FLT_MASK5: 1;  // bits 4 to 4
	uint8_t AUX_FLT_MASK6: 1;  // bits 5 to 5
	uint8_t AUX_FLT_MASK7: 1;  // bits 6 to 6
	uint8_t AUX_FLT_MASK8: 1;  // bits 7 to 7
} stc_MASK_DIAG_S4_field_by_channel_t;

typedef union un_MASK_DIAG_S4 {
    uint8_t u8Register;
    stc_MASK_DIAG_S4_field_t stcField;
    stc_MASK_DIAG_S4_field_by_channel_t stcFieldByChannel;
} un_MASK_DIAG_S4_t;

typedef struct stc_MASK_DIAG_S12_field {
    uint8_t rsv_0_5: 6;  // bits 0 to 5
    uint8_t CB_PFAIL_MASK: 1;  // bits 6 to 6
    uint8_t ADC_PFAIL_MASK: 1;  // bits 7 to 7
} stc_MASK_DIAG_S12_field_t;

typedef union un_MASK_DIAG_S12 {
    uint8_t u8Register;
    stc_MASK_DIAG_S12_field_t stcField;
} un_MASK_DIAG_S12_t;

typedef struct stc_MASK_DIAG_S13_field {
    uint8_t DVDD_UV_MASK: 1;  // bits 0 to 0
    uint8_t DVDD_OV_MASK: 1;  // bits 1 to 1
    uint8_t AVDD_UV_MASK: 1;  // bits 2 to 2
    uint8_t AVDD_OV_MASK: 1;  // bits 3 to 3
    uint8_t OSC_ERR_MASK: 1;  // bits 4 to 4
    uint8_t FUSE_CRC_ERR_MASK: 1;  // bits 5 to 5
    uint8_t GND_OW_FAIL_MASK: 1;  // bits 6 to 6
    uint8_t rsv_7_7: 1;  // bits 7 to 7
} stc_MASK_DIAG_S13_field_t;

typedef union un_MASK_DIAG_S13 {
    uint8_t u8Register;
    stc_MASK_DIAG_S13_field_t stcField;
} un_MASK_DIAG_S13_t;

typedef struct stc_FAULT_MASK_field {
    uint8_t MSK_SYS: 1;  // bits 0 to 0
    uint8_t rsv_1_4: 4;  // bits 1 to 4
    uint8_t MSK_AUX_ADC: 1;  // bits 5 to 5
    uint8_t MSK_CS_ADC: 1;  // bits 6 to 6
    uint8_t MSK_PARITY: 1;  // bits 7 to 7
} stc_FAULT_MASK_field_t;

typedef union un_FAULT_MASK {
    uint8_t u8Register;
    stc_FAULT_MASK_field_t stcField;
} un_FAULT_MASK_t;

typedef struct stc_CLR_FLT_field {
    uint8_t CLR_SYS: 1;  // bits 0 to 0
    uint8_t rsv_1_4: 4;  // bits 1 to 4
    uint8_t CLR_AUX: 1;  // bits 5 to 5
    uint8_t CLR_CVS: 1;  // bits 6 to 6
    uint8_t CLR_PARITY: 1;  // bits 7 to 7
} stc_CLR_FLT_field_t;

typedef union un_CLR_FLT {
    uint8_t u8Register;
    stc_CLR_FLT_field_t stcField;
} un_CLR_FLT_t;

typedef struct stc_GPIO_STAT1_field {
	uint8_t GPIO_8_1; // bits 7 to 0
} stc_GPIO_STAT1_field_t;

typedef struct stc_GPIO_STAT1_field_by_io {
	uint8_t GPIO1: 1;  // bits 0 to 0
	uint8_t GPIO2: 1;  // bits 1 to 1
	uint8_t GPIO3: 1;  // bits 2 to 2
	uint8_t GPIO4: 1;  // bits 3 to 3
	uint8_t GPIO5: 1;  // bits 4 to 4
	uint8_t GPIO6: 1;  // bits 5 to 5
	uint8_t GPIO7: 1;  // bits 6 to 6
	uint8_t GPIO8: 1;  // bits 7 to 7
} stc_GPIO_STAT1_field_by_io_t;

typedef union un_GPIO_STAT1 {
    uint8_t u8Register;
    stc_GPIO_STAT1_field_t stcField;
    stc_GPIO_STAT1_field_by_io_t stcFieldByIO;
} un_GPIO_STAT1_t;

typedef struct stc_GPIO_STAT2_field {
	uint8_t GPIO_11_9: 3; // bits 2 to 0
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_STAT2_field_t;

typedef struct stc_GPIO_STAT2_field_by_io {
	uint8_t GPIO9: 1;  // bits 0 to 0
	uint8_t GPIO10: 1;  // bits 1 to 1
	uint8_t GPIO11: 1;  // bits 2 to 2
    uint8_t rsv_3_7: 5;  // bits 3 to 7
} stc_GPIO_STAT2_field_by_io_t;

typedef union un_GPIO_STAT2 {
    uint8_t u8Register;
    stc_GPIO_STAT2_field_t stcField;
    stc_GPIO_STAT2_field_by_io_t stcFieldByIO;
} un_GPIO_STAT2_t;

typedef struct
{
    int16_t address;
    uint8_t read_mask;
    uint8_t write_mask;
    uint8_t def_value;
    uint8_t cur_value;
} RegConfig;

extern RegConfig shq89718_regs[];


#endif // SHQ89718_REGISTERS_HDR
