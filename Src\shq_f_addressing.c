#include <stdint.h>
#include <string.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 菊花链地址分配函数
 *
 * 为菊花链中的设备分配地址，支持正向和反向操作
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 方向设置 (0为正向, 1为反向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_addressing(uintptr_t afe_ctx, int8_t dir)
{
    int32_t ret;
    uint8_t *p_ret;
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;
    uint8_t stack_count;
    un_COMM_CTRL_t comm_ctrl;
    un_CONTROL1_t control1;
    un_ADC_CONF_t adc_conf;
    un_COMM_CONF_t comm_conf;

    // 1. 设置方向并清除计数器
    control1.u8Register = 0;
    control1.stcField.DIR_SEL = dir ? 1 : 0;
    broadcast_write(R_CONTROL1, &control1.u8Register, 1);
    simple_delay_ms(1);

    if (dir) {
        p_afe->stack_count_bwd = 0;
        p_afe->stack_err_addr_bwd = 0;
    } else {
        p_afe->stack_count_fwd = 0;
        p_afe->stack_err_addr_fwd = 0;
    }

    // 2. 根据方向设置通信控制
    if (dir) {
        // 反向：清除顶部并设置反向方向
        comm_ctrl.u8Register = 0;
        broadcast_write_reverse(R_COMM_CTRL, &comm_ctrl.u8Register, 1);
        control1.u8Register = 0;
        control1.stcField.DIR_SEL = 1;
        broadcast_write_reverse(R_CONTROL1, &control1.u8Register, 1);
        simple_delay_ms(1);
    } else {
        // 正向：清除顶部并设置正向方向
        comm_ctrl.u8Register = 0;
        broadcast_write(R_COMM_CTRL, &comm_ctrl.u8Register, 1);
        control1.u8Register = 0;
        broadcast_write(R_CONTROL1, &control1.u8Register, 1);
        simple_delay_ms(1);
    }

    // 3. 开始自动地址分配
    control1.u8Register = 0;
    control1.stcField.ADDR_WR = 1;
    control1.stcField.DIR_SEL = dir ? 1 : 0;
    ret = broadcast_write(R_CONTROL1, &control1.u8Register, 1);
    simple_delay_ms(1);

    // 尝试合并4.7.实现自动获取stack数量，但失败了
    // ret = broadcast_write(dir ? R_DIR1_ADDR : R_DIR0_ADDR, 0, 1);
    // stack_count = 0;
    // int stack_addr = 1;
    // while (1) {
    //     ret = broadcast_write(dir ? R_DIR1_ADDR : R_DIR0_ADDR, &stack_addr, 1);
    //     // simple_delay_ms(1);
    //     ret = single_dev_read(stack_addr, dir ? R_DIR1_ADDR : R_DIR0_ADDR, 1, &p_ret);
        
    //     // 安全检查：验证指针和返回值
    //     if (ret == 1 && p_ret != NULL) {
    //         print("inf: stack%d: %d %d\n", stack_addr, ret, ((DSC_COMM_FRAME_HDR*)p_ret)->data[0]);
    //         if (((DSC_COMM_FRAME_HDR*)p_ret)->data[0] == stack_addr) {
    //             stack_count++;
    //             stack_addr++;
    //             broadcast_write(R_CONTROL1, &control1.u8Register, 1);
    //         } else {
    //             break;
    //         }
    //     } else {
    //         print("err: single_dev_read failed for stack %d, ret=%d\n", stack_addr, ret);
    //         break;
    //     }
    // }

    // 4. 执行自动地址分配
    for (stack_count = 0; stack_count <= p_afe->stack_count; stack_count++) {
        ret = broadcast_write(dir ? R_DIR1_ADDR : R_DIR0_ADDR, &stack_count, 1);
    }

    // 5. 等待地址分配完成
    simple_delay_ms(1);

    // 6. 设置堆栈计数以控制超时
    adc_conf.u8Register = 0;
    adc_conf.stcField.STACK_DEV_NUM = SHQ_DSC_BASE_SNUM;
    single_dev_write(0, R_ADC_CONF, &adc_conf.u8Register, 1);

    // 7. 验证地址分配结果
    stack_count = 0;
    while (stack_count < p_afe->stack_count) {
        ret = single_dev_read(stack_count + 1, dir ? R_DIR1_ADDR : R_DIR0_ADDR, 1, &p_ret);
        // 安全检查：验证指针和返回值
        if (ret == 1 && p_ret != NULL) {
            print("inf: %d: %d %d\n", stack_count + 1, ret, ((DSC_COMM_FRAME_HDR*)p_ret)->data[0]);
            if (((DSC_COMM_FRAME_HDR*)p_ret)->data[0] == stack_count + 1) {
                stack_count++;
            } else {
                break;
            }
        } else {
            print("err: single_dev_read failed for stack %d, ret=%d\n", stack_count + 1, ret);
            break;
        }
    }

    // 8. 更新堆栈计数
    if (dir) {
        p_afe->stack_count_bwd = stack_count;
        print("inf: bwd stacks %d\n", stack_count);
    } else {
        p_afe->stack_count_fwd = stack_count;
        print("inf: fwd stacks %d\n", stack_count);
    }

    // 9. 处理地址分配错误
    if (stack_count != p_afe->stack_count) {
        print("inf: stack_err++\n");
        if (dir) {
            p_afe->stack_err_addr_bwd = stack_count + 1;
        } else {
            p_afe->stack_err_addr_fwd = stack_count + 1;
        }
        simple_delay_ms(2);
        dev_comm_clear();
    }

    // 10. 获取最终的堆栈计数
    if (dir) {
        stack_count = p_afe->stack_count_bwd;
        print("inf: bwd stacks %d\n", stack_count);
    } else {
        stack_count = p_afe->stack_count_fwd;
        print("inf: fwd stacks %d\n", stack_count);
    }

    // 11. 检查是否找到堆栈
    if (stack_count < 1) {
        print("inf: no stack found.\n");
        return COMME_STACK_ERROR;
    }

    // 12. 设置顶部堆栈
    comm_ctrl.u8Register = 0;
    comm_ctrl.stcField.STACK_DEV = 1;
    comm_ctrl.stcField.TOP_STACK = 1;
    single_dev_write(stack_count, R_COMM_CTRL, &comm_ctrl.u8Register, 1);

    // 13. 检查基础设备，当设置stack数量比实际数量少会出错
    // ret = single_dev_read(0, R_PART_ID, 1, &p_ret);
    // if (ret != 1) {
    //     print("inf: base error. ret=%d\n", ret);
    //     return COMME_BASE_ERROR;
    // }

    // 14. 设置堆栈计数
    adc_conf.u8Register = 0;
    adc_conf.stcField.STACK_DEV_NUM = stack_count;
    ret = stack_write(R_ADC_CONF, &adc_conf.u8Register, 1);

    // 15. 最终验证测试
    ret = single_dev_read(stack_count, dir ? R_DIR1_ADDR : R_DIR0_ADDR, 1, &p_ret);
    // 安全检查：验证指针和返回值
    if (ret == 1 && p_ret != NULL) {
        print("inf: re-check %d: %d %d\n", stack_count, ret, ((DSC_COMM_FRAME_HDR*)p_ret)->data[0]);
        if (((DSC_COMM_FRAME_HDR*)p_ret)->data[0] != stack_count) {
            print("inf: addressing failed.\n");
            return COMME_ADDRESSING_FAILED;
        }
    } else {
        print("err: final verification failed, ret=%d\n", ret);
        return COMME_ADDRESSING_FAILED;
    }

    // 16. 配置通信设置
    comm_conf.u8Register = 0;
#ifdef AFE_DIS_WDT
    comm_conf.stcField.WDT_DIS = 1; // 仅调试时禁用WDT
#endif
    stack_write(R_COMM_CONF, &comm_conf.u8Register, 1);

    // 17. 最终检查堆栈计数
    if (dir) {
        if (p_afe->stack_count_bwd < p_afe->stack_count) {
            return COMME_STACK_ERROR;
        }
    } else {
        if (p_afe->stack_count_fwd < p_afe->stack_count) {
            return COMME_STACK_ERROR;
        }
    }

    return 0;
}

