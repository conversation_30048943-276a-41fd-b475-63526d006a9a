#include "std_hal.h"
#include "usb_xup.h"

#if defined(STM32F401xC) || defined(STM32F401RCTx)
#include "stm32f4xx_hal_conf.h"
#endif

#ifdef STM32L433xx
#include "stm32l4xx_hal_conf.h"
#endif

#ifdef STM32G431xx
#include "stm32g4xx_hal_conf.h"
#endif

#include "shq_afe_task.h"

#include "platform/comm_io.h"

/*

// from 0x20004400 there are 10k bytes for code
gcc build codesample
mem load 0x20004400 codesample/sample.elf.bin
ssc memc 0x20004404
ssc memc 0x20004408

// start
ssc cmdr
ssc cmd1

// stop
ssc cmd0

// if you want to reload new code, stop first
ssc memc 0x2000440c

*/

// begin must have
//static const STDHAL *hal = (STDHAL*)0x800FC00;
extern void __libc_init_array (void);
uint32_t SystemCoreClock;
// end must have

#include <math.h>
#include <string.h>
#include <stdio.h>


int usr_init(void);
int usr_uninit(void);

// entrance table
__attribute__((section(".sec_entry"))) void * fun[] = {
	(void*)0x55aa55aa, // flag, flag here for verifying whether the mcu rebooted that youa have to download the program
	__libc_init_array, // c lib init
	usr_init, // functions
	usr_uninit,
};

/* removed because there is no space for standard printf
int __io_putchar(char ch)
{
	hal->vc_tx(1, (unsigned char *)&ch);
	return 0;
}

int __io_getchar(void)
{
	return 0;
}
*/

#define GPIO_SPEED_VALUE GPIO_SPEED_FREQ_LOW // GPIO_SPEED_FREQ_MEDIUM
#define GPIO_SPEED_SPI   GPIO_SPEED_FREQ_HIGH
#define IOGC(arg) (arg - 'A')

static const uint32_t init_table[] = {
	// uart is not using
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_9|GPIO_PIN_10,  GPIO_MODE_INPUT, /* value */ 0, GPIO_NOPULL, /* alt */ 0, GPIO_SPEED_VALUE,
	// spi/uart sel, dry
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_1, GPIO_MODE_INPUT, /* value */ 1, GPIO_PULLUP, /* alt */ 0, GPIO_SPEED_VALUE,
	// wakeup, not using
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_2, GPIO_MODE_INPUT, /* value */ 1, GPIO_PULLUP, /* alt */ 0, GPIO_SPEED_VALUE,
	// nFault
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_3, GPIO_MODE_INPUT, /* value */ 1, GPIO_PULLUP, /* alt */ 0, GPIO_SPEED_VALUE,
	// nFault - AFE
	GPIO_CONFIG, IOGC('B'), GPIO_PIN_5, GPIO_MODE_INPUT, /* value */ 1, GPIO_PULLUP, /* alt */ 0, GPIO_SPEED_VALUE,
	// spi cs
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_4, GPIO_MODE_OUTPUT_PP, /* value */ 1, GPIO_NOPULL, /* alt */ 0, GPIO_SPEED_VALUE,
	// spi io
	GPIO_CONFIG, IOGC('A'), GPIO_PIN_5|GPIO_PIN_6|GPIO_PIN_7, GPIO_MODE_AF_PP, /* value */ 0, GPIO_NOPULL, /* alt */ GPIO_AF5_SPI1, GPIO_SPEED_SPI,
	// spi
	SPI_CONFIG, /* bus */ 0, /* rx only */ 0, /*data_8bit*/ 1, /* clk_pol_h */ 0, /* clk_pha_is_0 */ 1, /* clk_div */ 32, /* msb_first */ 1,
	0, 0, 0, 0, 0, 0, 0, 0
};

extern int32_t cmd_proc(uint32_t cmd, uint8_t *buf, int32_t buf_size, int8_t *rep, uint32_t argc, uint8_t *argv[]);

int usr_init(void)
{
	// update system core clock
	SystemCoreClock = *hal->pSystemCoreClock;

	usr_uninit();

	// set interrupt and add isr
	hal->hal_exec_table(init_table);

	shq_afe_task_init();

	// add command proc
	hal->hook_cmd_proc((uint32_t)cmd_proc);

	// set task calls
	hal->set_board_calls((uint32_t)shq_afe_task_run, 0, 0);

	// need tick call in 1ms
	hal->isr_set(ISR_SYSTICK, (uint32_t)shq_systick_task);

	return 0;
}

int usr_uninit(void)
{
	// remove task calls
	hal->set_board_calls(0, 0, 0);

	// remove command
	hal->hook_cmd_proc(0);

	// remove tick call
	hal->isr_set(ISR_SYSTICK, 0);

	return 0;
}
