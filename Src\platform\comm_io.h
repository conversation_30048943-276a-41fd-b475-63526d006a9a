#ifndef COMM_IOB_HDR
#define COMM_IOB_HDR

#include <stdint.h>

#ifdef __cplusplus
extern "C"
{
#endif

#define STACK_COUNT 1  // AFE devices count

#define DSC_MAX_STACKS_COUNT STACK_COUNT // max stack devices to access, used for communication buffer
#define DSC_MAX_FRAME_DATA_SIZE 128 // 128 is the max size of the protocol, you can define smaller size according to your operations e.g. 72

#define DSC_MAX_FRAME_SIZE (DSC_MAX_FRAME_DATA_SIZE + 6) // max frame size
#define DSC_COMM_BUFFER_SIZE ((DSC_MAX_STACKS_COUNT + 1) * DSC_MAX_FRAME_SIZE) // buffer size for single/stack/broadcast operations

#define KEEP_ALL_DEV_VOLTS // save all voltages for all AFE
#define KEEP_INDEXED_VOLTS // save indexed volts, use channel map, not divided by AFE, skip bus-bar
#define INDEXED_CELLS_COUNT (STACK_COUNT*18) // space for indexed voltage store
// #define KEEP_ADC_CODES // save ADC code

#define MAX_CELLS_PER_AFE 18 // 单个AFE通道数
#define MAX_GPIOS_PER_AFE 11

#define TYPE_CADC_DATA      1
#define TYPE_SADC_DATA      2
#define TYPE_GPIO_AUX_DATA  3
#define TYPE_GPIO_AUX2_DATA 4

#define LIMIT_CELL_OW           500 // Unit: mV
#define LIMIT_CS_PIN_SHORT_DOWN 300 // Unit: mV
#define LIMIT_CS_PIN_SHORT_UP   800 // Unit: mV
#define LIMIT_CS_PIN_SHORT_DIFF 500 // Unit: mV
#define LIMIT_FET_OPEN          500 // Unit: mV
#define LIMIT_FET_SHORT         150 // Unit: mV
#define LIMIT_ICURR_POWER_FAIL  150 // Unit: mV
#define LIMIT_NTC_SHORT_GND     500 // Unit: mV
#define LIMIT_NTC_OW            1000 // Unit: mV
#define LIMIT_NTC_SHORT_POWER   300  // Unit: mV

#define LIMIT_CELL_OW_HIGH(input_volts) (input_volts/10 * 12/10)
#define LIMIT_CELL_OW_LOW(input_volts) (input_volts/10 * 8/10)

#define AFE_DIS_WDT         // disable watchdog for AFE
#define BASE_DIS_WDT        // disable watchdog for base

void delay_us(uint32_t useconds);

#ifdef STM32

#include "std_hal.h"

extern const STDHAL *hal;
#define print(...) do{ hal->vc_printf(__VA_ARGS__); } while(0)
void shq_systick_task(void);

#endif

// if you dont want the print
//#undef print
//#define print(...)

#define PRINT_DSC_PACKAGES // print tx/rx packages for debug

int32_t dev_send_pulse(uint32_t width_us);
int32_t dev_spi_trans(uint8_t *data_tx, uint32_t numTxBytes, uint8_t *data_rx, uint32_t numRxFrameBytes, uint32_t numRxFrames, int32_t usDelayBeforeRead);
int32_t dev_spi_trans_times(uint8_t *data_tx, uint32_t numTxBytes, uint8_t *data_rx, uint32_t numRxFrameBytes, uint32_t numRxFrames, int32_t usDelayBeforeRead,uint8_t* call_last);
int32_t dev_is_ready();
int32_t dev_comm_clear();
int32_t dev_start_long_pulse(uint8_t width_ms);
int32_t dev_is_long_pulse_done();

#ifdef __cplusplus
}
#endif

#endif // COMM_IOB_HDR
