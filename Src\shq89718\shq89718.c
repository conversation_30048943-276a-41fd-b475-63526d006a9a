#include <stdint.h>
#include <stdbool.h>
#include "../platform/comm_io.h"
#include "shq89xx.h"
#include "shq89718_registers.h"
#include "shq89718.h"


// 全局初始化
void shq89718_global_init(void)
{
    
}


// log2计算
static uint8_t log2_fast(const uint32_t val)
{
    if (val == 0) return 0;
    uint8_t result = 0;
    uint32_t temp = val;
    while (temp > 1) {
        temp >>= 1;
        result++;
    }
    return result;
}

/**
 * @brief 寄存器读取函数
 *
 * 从指定设备的寄存器地址读取多个字节
 *
 * @param devAddress 设备地址
 * @param regAddress 寄存器地址
 * @param numBytes 要读取的字节数
 * @param pRetBuf 存储读取数据的指针
 * @return >=0表示成功读取的字节数, <0表示错误
 */
int32_t shq_reg_read(int8_t devAddress, uint16_t regAddress, uint8_t numBytes, uint8_t **pRetBuf)
{
    uint8_t *p_frame_buf;
    int32_t ret = single_dev_read(devAddress, regAddress, numBytes, &p_frame_buf);
    if (pRetBuf) {
        (*pRetBuf) = p_frame_buf + DSC_FRAME_DATA_OFFSET;
    }
    return ret;
}

/**
 * @brief 寄存器写入函数
 *
 * 向指定设备的寄存器写入单个字节
 *
 * @param devAddress 设备地址
 * @param regAddress 寄存器地址
 * @param regValue 要写入的值
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_reg_write(int8_t devAddress, uint16_t regAddress, uint8_t regValue)
{
    if (devAddress == DEV_ADDR_STACKS) {
        return stack_write(regAddress, &regValue, 1);
    } else if (devAddress == DEV_ADDR_BROADCAST) {
        return broadcast_write(regAddress, &regValue, 1);
    } else {
        return single_dev_write(devAddress, regAddress, &regValue, 1);
    }
}

/**
 * @brief 寄存器位更新函数
 *
 * 读取寄存器，修改指定位，然后写回
 *
 * @param devAddress 设备地址
 * @param regAddress 寄存器地址
 * @param bitMask 位掩码
 * @param bitValue 新的位值
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_reg_update(int8_t devAddress, uint16_t regAddress, uint8_t bitMask, uint8_t bitValue)
{
    uint8_t *pRetBuf;
    int32_t ret = single_dev_read(devAddress, regAddress, 1, &pRetBuf);
    if (ret != 1) return ret;

    bitValue &= bitMask;
    bitValue |= (((DSC_COMM_FRAME_HDR*)pRetBuf)->data[0] & (~bitMask));

    return single_dev_write(devAddress, regAddress, &bitValue, 1);
}

/**
 * @brief 设备ID获取函数
 *
 * 读取并返回设备的部件ID
 *
 * @param dev_addr AFE设备地址
 * @return >=0表示设备ID, <0表示错误
 */
int32_t shq_get_id(int8_t dev_addr)
{
    uint8_t *pRet;
    if (1 == single_dev_read(dev_addr, R_PART_ID, 1, &pRet)) {
        return ((DSC_COMM_FRAME_HDR*)pRet)->data[0];
    }
    return -1;
}

/**
 * @brief 看门狗定时器设置函数
 *
 * 启用/禁用看门狗定时器
 *
 * @param dev_addr AFE设备地址
 * @param enable true启用看门狗, false禁用看门狗
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_set_wdt(int8_t dev_addr, bool enable)
{
    int32_t ret;
    uint8_t *pReadBuffer;
    un_COMM_CONF_t comm_conf;

    ret = shq_reg_read(dev_addr, R_COMM_CONF, 1, &pReadBuffer);
    if (ret != 1) return ret;

    comm_conf.u8Register = pReadBuffer[0];
    comm_conf.stcField.WDT_DIS = enable ? 0 : 1;

    return shq_reg_write(dev_addr, R_COMM_CONF, comm_conf.u8Register);
}

/**
 * @brief ADC滤波器配置函数
 *
 * 配置ADC的滤波器设置
 *
 * @param dev_addr AFE设备地址
 * @param aux_osr_512 GP/AUX ADC是否使用512 OSR
 * @param cs_osr_512 VC/CB ADC是否使用512 OSR
 * @param vc_lpf_osr VC ADC低通滤波器设置
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_set_adc_filters(int8_t dev_addr, bool aux_osr_512, bool cs_osr_512, uint16_t vc_lpf_osr)
{
    un_ADC_CTRL1_t adc_ctrl1;
    adc_ctrl1.u8Register = 0;
    adc_ctrl1.stcField.GP_DR = aux_osr_512 ? 1 : 0;
    adc_ctrl1.stcField.CS_DR = cs_osr_512 ? 1 : 0;
    adc_ctrl1.stcField.LPF_VCELL = 1 + log2_fast(vc_lpf_osr >> 4);
    return shq_reg_write(dev_addr, R_ADC_CTRL1, adc_ctrl1.u8Register);
}

/**
 * @brief ADC模式设置函数
 *
 * 配置ADC的操作模式
 *
 * @param dev_addr AFE设备地址
 * @param all_freeze 是否冻结结果寄存器
 * @param cs_adc_mode VC和CB ADC模式 (0=停止, 1=单次, 2=连续, 3=CB ADC一次)
 * @param gp_adc_mode GP和AUX ADC模式 (0=轮询, 1-11=指定GPIO, 其他=特殊通道)
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_set_adc_mode(int8_t dev_addr, bool all_freeze, int8_t cs_adc_mode, int8_t gp_adc_mode)
{
    un_ADC_CTRL2_t adc_ctrl2;
    adc_ctrl2.u8Register = 0;
    adc_ctrl2.stcField.FREEZE_EN = all_freeze ? 1 : 0;
    adc_ctrl2.stcField.CS_ADC_MODE = cs_adc_mode & 0x03;
    adc_ctrl2.stcField.GPIO_MUX_SEL = gp_adc_mode & 0x1f;
    return shq_reg_write(dev_addr, R_ADC_CTRL2, adc_ctrl2.u8Register);
}


/**
 * @brief ADC启动函数
 *
 * 触发ADC开始转换
 *
 * @param dev_addr AFE设备地址
 * @param cs_adc_go 是否启动VC/CB ADC转换
 * @param gp_adc_go 是否启动GP/AUX ADC转换
 * @return >=0表示成功, <0表示错误
 */
int32_t shq_set_adc_start(int8_t dev_addr, bool cs_adc_go, bool gp_adc_go)
{
    un_ADC_CTRL5_t adc_ctrl5;
    adc_ctrl5.u8Register = 0;
    adc_ctrl5.stcField.CS_ADC_GO = cs_adc_go ? 1 : 0;
    adc_ctrl5.stcField.GP_ADC_GO = gp_adc_go ? 1 : 0;
    return shq_reg_write(dev_addr, R_ADC_CTRL5, adc_ctrl5.u8Register);
}

/**
 * @brief ADC状态获取函数
 *
 * 获取ADC工作状态
 *
 * @param dev_addr AFE设备地址
 * @return >=0表示成功(返回状态值), <0表示错误
 */
int32_t shq_get_adc_status(int8_t dev_addr)
{
    int32_t ret;
    uint8_t *pReadBuffer;
    ret = shq_reg_read(dev_addr, R_ADC_STAT, 1, &pReadBuffer);
    if (ret != 1) return ret;
    return pReadBuffer[0];
}
