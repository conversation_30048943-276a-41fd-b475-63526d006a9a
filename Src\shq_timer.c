#include <stdint.h>
#include "shq_timer.h"

#define TIMER_COUNT 4
static int16_t timer_instances[TIMER_COUNT] = {-1, -1, -1, -1};

/**
 * @brief Handles the 1 millisecond system tick event.
 * 
 * This function is called periodically to handle the 1 millisecond system tick event.
 * It decrements the value of all active timers by 1 millisecond.
 * If a timer reaches 0, it remains at 0.
 * 
 * Call this function if you need to use these timer functions.
 * 
 * @return None.
 */
void on_system_tick(void)
{
	for(int8_t i=0; i<TIMER_COUNT; i++) {
		if (timer_instances[i] >= 0) {
			--timer_instances[i];
		}
	}
}

/**
 * @brief Allocates a free timer instance.
 * 
 * This function searches through an array of timer instances to find a free one.
 * If a free timer is found, it initializes it and returns its index.
 * If no free timers are available, it returns -1.
 * 
 * @return The index of the allocated timer if successful, or -1 if no timers are available.
 */
int8_t get_timer() {
	for(int8_t i=0; i<TIMER_COUNT; i++) {
		if (timer_instances[i] < 0) {
			timer_instances[i] = 0;
			return i;
		}
	}
	return -1;
}

/**
 * @brief Sets the value of a specified timer.
 * 
 * This function sets the value of the specified timer to the given number of milliseconds.
 * It updates the `timer_instances` array with the new value.
 * 
 * @param timer The index of the timer to set.
 * @param milliseconds The number of milliseconds to set the timer to.
 * 
 * @return None.
 */
void set_timer(int8_t timer, int16_t milliseconds)
{
	timer_instances[timer] = milliseconds;
}

/**
 * @brief Frees a specified timer.
 * 
 * This function marks the specified timer as free by setting its value to -1 in the `timer_instances` array.
 * 
 * @param timer The index of the timer to free.
 * 
 * @return None.
 */
void put_timer(int8_t timer)
{
	timer_instances[timer] = -1;
}

/**
 * @brief Checks the value of a specified timer.
 * 
 * This function retrieves the current value of the specified timer from the `timer_instances` array.
 * 
 * @param timer The index of the timer to check.
 * 
 * @return The current value of the timer in milliseconds.
 */
int16_t check_timer(int8_t timer)
{
	return timer_instances[timer];
}

// 简化的延时函数
void simple_delay_ms(int16_t milliseconds)
{
    int8_t timer_id = get_timer();
    if (timer_id >= 0) {
        set_timer(timer_id, milliseconds);
        while (check_timer(timer_id) > 0) {
            // 等待延时完成
        }
    }
}
