/**
 * @file spi_test_example.c
 * @brief SPI测试延时功能使用示例
 * 
 * 本文件展示如何使用SPI测试延时功能来调试通信时序问题
 */

#include <stdint.h>
#include "shq_afe_task.h"
#include "platform/comm_io.h"

/**
 * @brief SPI测试延时使用示例
 * 
 * 演示如何通过cmd_proc函数配置和使用SPI测试延时功能
 */
void spi_test_delay_example(void)
{
    uint8_t *argv[4];
    uint8_t arg0[] = "10";    // 片选-时钟延时 10us
    uint8_t arg1[] = "5";     // 字节间延时 5us  
    uint8_t arg2[] = "100";   // 命令间延时 100us
    uint8_t arg3[] = "1";     // 启用测试延时
    
    argv[0] = arg0;
    argv[1] = arg1;
    argv[2] = arg2;
    argv[3] = arg3;
    
    print("inf: =====SPI测试延时功能示例=====\n");
    
    // 1. 查询当前配置
    print("inf: 1. 查询当前延时配置:\n");
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 2. 设置测试延时
    print("inf: 2. 设置测试延时配置:\n");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    
    // 3. 再次查询确认配置
    print("inf: 3. 确认新的延时配置:\n");
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 4. 禁用测试延时
    print("inf: 4. 禁用测试延时:\n");
    uint8_t disable_arg[] = "0";
    argv[3] = disable_arg;
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    
    // 5. 最终查询
    print("inf: 5. 最终配置状态:\n");
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    print("inf: =====SPI测试延时示例完成=====\n");
}

/**
 * @brief 不同测试场景的延时配置示例
 */
void spi_test_scenarios(void)
{
    uint8_t *argv[4];
    uint8_t cs_arg[10], byte_arg[10], cmd_arg[10], enable_arg[2];
    
    argv[0] = cs_arg;
    argv[1] = byte_arg;
    argv[2] = cmd_arg;
    argv[3] = enable_arg;
    
    print("inf: =====SPI测试场景示例=====\n");
    
    // 场景1: 快速测试 - 小延时
    print("inf: 场景1: 快速测试配置\n");
    strcpy((char*)cs_arg, "1");
    strcpy((char*)byte_arg, "1");
    strcpy((char*)cmd_arg, "10");
    strcpy((char*)enable_arg, "1");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 场景2: 中等延时测试
    print("inf: 场景2: 中等延时测试配置\n");
    strcpy((char*)cs_arg, "50");
    strcpy((char*)byte_arg, "20");
    strcpy((char*)cmd_arg, "500");
    strcpy((char*)enable_arg, "1");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 场景3: 慢速测试 - 大延时
    print("inf: 场景3: 慢速测试配置\n");
    strcpy((char*)cs_arg, "1000");
    strcpy((char*)byte_arg, "500");
    strcpy((char*)cmd_arg, "5000");
    strcpy((char*)enable_arg, "1");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 场景4: 只测试片选-时钟延时
    print("inf: 场景4: 只测试片选-时钟延时\n");
    strcpy((char*)cs_arg, "100");
    strcpy((char*)byte_arg, "0");
    strcpy((char*)cmd_arg, "0");
    strcpy((char*)enable_arg, "1");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 场景5: 只测试字节间延时
    print("inf: 场景5: 只测试字节间延时\n");
    strcpy((char*)cs_arg, "0");
    strcpy((char*)byte_arg, "50");
    strcpy((char*)cmd_arg, "0");
    strcpy((char*)enable_arg, "1");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    // 恢复正常模式
    print("inf: 恢复正常模式\n");
    strcpy((char*)enable_arg, "0");
    cmd_proc('tdly', NULL, 0, NULL, 4, argv);
    cmd_proc('qdly', NULL, 0, NULL, 0, NULL);
    
    print("inf: =====SPI测试场景示例完成=====\n");
}

/**
 * @brief 测试延时功能的使用说明
 */
void spi_test_usage_help(void)
{
    print("inf: =====SPI测试延时功能使用说明=====\n");
    print("inf: \n");
    print("inf: 1. 设置测试延时命令: tdly <cs_clock_us> <byte_us> <cmd_us> <enable>\n");
    print("inf:    - cs_clock_us: 片选和时钟之间的延时(微秒), 范围0-10000\n");
    print("inf:    - byte_us: 字节之间的延时(微秒), 范围0-10000\n");
    print("inf:    - cmd_us: 命令之间的延时(微秒), 范围0-10000\n");
    print("inf:    - enable: 是否启用测试延时, 0=禁用, 1=启用\n");
    print("inf: \n");
    print("inf: 2. 查询当前配置命令: qdly\n");
    print("inf: \n");
    print("inf: 3. 使用示例:\n");
    print("inf:    tdly 10 5 100 1  # 设置片选-时钟延时10us, 字节间延时5us, 命令间延时100us, 启用\n");
    print("inf:    qdly             # 查询当前配置\n");
    print("inf:    tdly 0 0 0 0     # 禁用所有测试延时\n");
    print("inf: \n");
    print("inf: 4. 测试场景建议:\n");
    print("inf:    - 快速测试: tdly 1 1 10 1\n");
    print("inf:    - 中等延时: tdly 50 20 500 1\n");
    print("inf:    - 慢速测试: tdly 1000 500 5000 1\n");
    print("inf:    - 只测片选: tdly 100 0 0 1\n");
    print("inf:    - 只测字节: tdly 0 50 0 1\n");
    print("inf:    - 只测命令: tdly 0 0 1000 1\n");
    print("inf: \n");
    print("inf: 5. 注意事项:\n");
    print("inf:    - 延时会显著影响通信性能，仅用于调试\n");
    print("inf:    - 最大延时限制为10ms(10000us)\n");
    print("inf:    - 测试完成后记得禁用延时功能\n");
    print("inf: \n");
    print("inf: =====使用说明完成=====\n");
}
