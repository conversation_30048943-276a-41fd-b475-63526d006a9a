#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 堆栈电芯索引计算
 *
 * 计算指定堆栈索引的全局电芯起始索引
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @return 全局电芯起始索引
 */
static int32_t simple_stack_cell_index(SHQ_AFE_DATA *p_afe, int8_t stack_index)
{
    int32_t cell_index = 0;
    for (int8_t i = 0; i < stack_index; i++) {
        for (int8_t j = 0; j < p_afe->cell_counts[i]; j++) {
            cell_index++;
        }
    }
    return cell_index;
}

/**
 * @brief 电芯数值保存函数
 *
 * 保存指定堆栈的电芯数值到数据结构中
 *
 * @param p_afe AFE数据结构指针
 * @param stack_index 堆栈索引
 * @param cell_index 全局电芯索引
 * @param pReadBuffer 读取缓冲区指针
 * @return 下一个全局电芯索引
 */
static int32_t simple_save_cs_values(SHQ_AFE_DATA *p_afe, int8_t stack_index, int32_t cell_index, uint8_t *pReadBuffer)
{
    int16_t code;
    int32_t volt_mv;

    for (int8_t j = 0; j < p_afe->cell_counts[stack_index]; j++) {
        // 处理电芯电压 (vc)
        code = (pReadBuffer[j * 2] << 8) + pReadBuffer[j * 2 + 1];
        volt_mv = shq_code_to_mV(code);

#if defined(KEEP_ALL_DEV_VOLTS)
        p_afe->all_dev_volts_vc[stack_index][j] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->all_dev_code_vc[stack_index][j] = code;
#endif
#endif

#if defined(KEEP_INDEXED_VOLTS)
        p_afe->indexed_vc_volts[cell_index] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->indexed_vc_code[cell_index] = code;
#endif
#endif

        // 处理均衡通道电压 (cb)
        code = (pReadBuffer[j * 2 + 36] << 8) + pReadBuffer[j * 2 + 36 + 1];
        volt_mv = shq_code_to_mV(code);

#if defined(KEEP_ALL_DEV_VOLTS)
        p_afe->all_dev_volts_cb[stack_index][j] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->all_dev_code_cb[stack_index][j] = code;
#endif
#endif

#if defined(KEEP_INDEXED_VOLTS)
        p_afe->indexed_cb_volts[cell_index] = volt_mv;
#if defined(KEEP_ADC_CODES)
        p_afe->indexed_cb_code[cell_index] = code;
#endif
#endif
        cell_index++;
    }
    return cell_index;
}

/**
 * @brief 电芯电压采样函数
 *
 * 执行指定方向的电芯ADC采样并保存结果
 *
 * @param afe_ctx AFE数据结构指针
 * @param dir 采样方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_cs_sampling(uintptr_t afe_ctx, int8_t dir)
{
    SHQ_AFE_DATA *p_afe = (SHQ_AFE_DATA *)afe_ctx;
    int32_t ret = 0;
    uint8_t *pReadBuffer;
    int32_t cell_index;
    int8_t stack_count;
    int8_t stack_index;

    // 1. 启动电芯ADC采样
    shq_set_adc_start(DEV_ADDR_STACKS, 1, 0);

    // 2. 等待ADC转换完成（18ms）
    simple_delay_ms(18);
    // 3. 获取堆栈计数
    stack_count = dir ? shq_get_bw_stack_count(p_afe) : shq_get_fw_stack_count(p_afe);

    if (stack_count < 1) {
        return 0;
    }

    // 4. 尝试堆栈读取
    ret = stack_read(R_FC1_HI, 72, stack_count, &pReadBuffer);
    print("inf: read buffer %d bytes\n", ret);    
    if (ret > 0) {
        // 堆栈读取成功
        pReadBuffer += ((72 + DSC_RESP_NONE_DATA_BYTES)*(stack_count - 1) + DSC_FRAME_DATA_OFFSET);
        stack_index = 0;
        cell_index = 0;
        while (1) {
            cell_index = simple_save_cs_values(p_afe, stack_index, cell_index, pReadBuffer);
            if (dir) {
                stack_index ++;
                if (stack_index >= p_afe->stack_count_bwd) {
                    break;
                }
                pReadBuffer -= (72 + DSC_RESP_NONE_DATA_BYTES);
            } else {
                stack_index ++;
                if (stack_index >= p_afe->stack_count_fwd) {
                    break;
                }
                pReadBuffer -= (72 + DSC_RESP_NONE_DATA_BYTES);
            }
        }
        // 跳过单独读取，直接到结束
        goto sampling_end;
    } else {
        // 堆栈读取失败，使用单独读取恢复
        dev_comm_clear();
        simple_delay_ms(1); // 调度延时
    }

    // 5. 单独读取每个堆栈
    stack_count = dir ? shq_get_bw_stack_count(p_afe) : shq_get_fw_stack_count(p_afe);

    if (dir) {
        stack_index = p_afe->stack_count - p_afe->stack_count_bwd;
        cell_index = simple_stack_cell_index(p_afe, stack_index);
    } else {
        stack_index = 0;
        cell_index = 0;
    }

    for (int8_t i = 1; i <= stack_count; i++) {
        ret = shq_reg_read(i, R_FC1_HI, 72, &pReadBuffer);

        if (ret != 72) {
            // 单独读取失败
            dev_comm_clear();

            if (dir) {
                p_afe->stack_err_addr_bwd = i;
            } else {
                p_afe->stack_err_addr_fwd = i;
            }
            break;
        }

        // 保存电芯数值
        cell_index = simple_save_cs_values(p_afe, stack_index, cell_index, pReadBuffer);

        if (dir) {
            stack_index++;
            cell_index = simple_stack_cell_index(p_afe, stack_index);
        } else {
            stack_index++;
        }
    }

sampling_end:
    // 6. 采样完成，可选的调试输出
#if defined(KEEP_ALL_DEV_VOLTS)
    // 可以在这里添加调试输出
    for (int i = 0; i < p_afe->stack_count; i++) {
        for (int j = 0; j < p_afe->cell_counts[i]; j++) {
            print("inf: all_dev_volts_vc[%d][%d] = %d\n", i, j, p_afe->all_dev_volts_vc[i][j]);
            print("inf: all_dev_volts_cb[%d][%d] = %d\n", i, j, p_afe->all_dev_volts_cb[i][j]);
#if defined(KEEP_ADC_CODES)
            print("inf: all_dev_code_vc[%d][%d] = %d\n", i, j, p_afe->all_dev_code_vc[i][j]);
            print("inf: all_dev_code_cb[%d][%d] = %d\n", i, j, p_afe->all_dev_code_cb[i][j]);
#endif // KEEP_ADC_CODES
        }
    }
#endif // KEEP_ALL_DEV_VOLTS

#if defined(KEEP_INDEXED_VOLTS)
    // 可以在这里添加索引电压的调试输出
    for (int i = 0; i < INDEXED_CELLS_COUNT; i++) {
        print("inf: indexed_vc_volts[%d] = %d\n", i, p_afe->indexed_vc_volts[i]);
        print("inf: indexed_cb_volts[%d] = %d\n", i, p_afe->indexed_cb_volts[i]);
#if defined(KEEP_ADC_CODES)
        print("inf: indexed_vc_code[%d] = %d\n", i, p_afe->indexed_vc_code[i]);
        print("inf: indexed_cb_code[%d] = %d\n", i, p_afe->indexed_cb_code[i]);
#endif // KEEP_ADC_CODES        
    }
#endif // KEEP_INDEXED_VOLTS

    return 0;
}
