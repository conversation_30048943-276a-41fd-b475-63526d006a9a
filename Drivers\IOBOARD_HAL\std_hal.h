/*
 * std_hal.h
 *
 *  Created on: Jul 2, 2022
 *      Author: user
 */

#ifndef INC_STD_HAL_H_
#define INC_STD_HAL_H_

#include <stdint.h>
#include <stdbool.h>

#define SSC_VER "FWv1.19"

#define DISABLE_IIO

#define DATA_BUF_SIZE 264

#ifdef STM32G031xx
#define MULTI_CMD_COUNT 1
#define COMM_RX_BUF_SIZE 1024
#define COMM_TX_BUF_SIZE 1024
#define NO_CMD_INTERACTIVE
#else
#define MULTI_CMD_COUNT 8 // max 8 commands for one batch
#define COMM_RX_BUF_SIZE 2048
#define COMM_TX_BUF_SIZE 2048
#endif

#define MAX_ARGV 72

/* spi/i2c access call, etc */
#define HAL_CALL_BUF_SIZE 268

#define SSC_CMD_PROC_BUF_SIZE (DATA_BUF_SIZE*2 + 4)
#define SSC_CMD_RX_BUF_SIZE (1280 + 64)


typedef enum tag_STD_HAL_ERRORS {
	ERR_MIN = -111,
	ERR_NOT_FOUND,
	ERR_UNKNOWN_CMD,
	ERR_PARAMETERS,
	ERR_I2CACCESS,
	ERR_I2C_ERROR,
	ERR_SPI_ERROR,
	ERR_TIM_ERROR,
	ERR_PWM_ERROR,
	ERR_UART_ERROR,
	ERR_HW_ERROR,
	ERR_HW_TIMEOUT,
	ERR_HW_BUSY,
	ERR_HW_UNKNOWN,
	ERR_OPT_TIMEOUT,
	ERR_MAX,
}STD_HAL_ERRORS;

typedef struct tag_RR_FIFO
{
	volatile uint32_t head;
	volatile uint32_t tail;
	uint32_t size;
}RR_FIFO;

#define RRFIFO_GET_R(pfifo)		((pfifo->tail) & (pfifo->size-1))
#define RRFIFO_GET_W(pfifo)		((pfifo->head) & (pfifo->size-1))
#define RRFIFO_INC_R(pfifo)		(++(pfifo->tail))
#define RRFIFO_INC_W(pfifo)		(++(pfifo->head))
#define RRFIFO_NOT_FULL(pfifo)	((pfifo->head + 1) != (pfifo->tail))
#define RRFIFO_NOT_EMPT(pfifo)	(pfifo->head != pfifo->tail)
#define RRFIFO_SIZE(pfifo) 		(pfifo->size)

enum ISR_IDX {
	ISR_XINT_0 = 0,
	ISR_XINT_1,
	ISR_XINT_2,
	ISR_XINT_3,
	ISR_XINT_4,
	ISR_XINT_9_5,
	ISR_XINT_15_10,
	ISR_SYSTICK,
	ISR_RTC_WAKEUP,// wakeup
	ISR_RTC_ALARM, // alarm
	ISR_TIM1_0, // BRK
	ISR_TIM1_1, // UP
	ISR_TIM1_2, // TRG
	ISR_TIM1_3, // CC
	ISR_TIM8_0, // BRK
	ISR_TIM8_1, // UP
	ISR_TIM8_2, // TRG
	ISR_TIM8_3, // CC
	ISR_TIM2,
	ISR_TIM3, // L433_LPTIM1
	ISR_TIM4, // L433_LPTIM2
	ISR_TIM5,
	ISR_TIM6,
	ISR_TIM7,
};

enum STD_HAL_API {
	FIDX_MIN = 0,
	MEM_READ = 1,
	MEM_WRITE,
	MEM_UPDATE,
	ISR_SET,
	ISR_CHECK,
	DECODER_CONFIG,
	DECODER_WRITE,
	RTC_READ_BACKUP_REG,
	RTC_WRITE_BACKUP_REG,
	GPIO_CONFIG,
	GPIO_WRITE,
	GPIO_TOGGLE,
	GPIO_READ,
	GPIO_ENABLE_IRQ,
	I2C_CONFIG,
	I2C_CS_CONFIG,
	I2C_CS,
	I2C_ENABLE,
	I2C_WRITE_EX,
	I2C_READ_EX,
	I2C_UPDATE_EX,
	SPI_CONFIG,
	SPI_CS_CONFIG,
	SPI_SET_TIMING,
	SPI_ENABLE,
	SPI_CS_VALID,
	SPI_CS,
	SPI_TRANS,
	SPI_COMMAND,
	SPI_COMMAND_EX,
	VC_TX,
	VC_PRINTF,
	RET_HEX,
	RET_BIN_INIT,
	RET_BIN,
	DELAY_SHORT,
	DELAY_INIT,
	DELAY_US,
	DELAY_MS,
	STR_SKIP_SPACE,
	STR_SKIP_CHAR,
	STR_TO_ARGV,
	STR_SCAN_UINT,
	SYS_DFU_MODE,
	GPIO_READ_PINS,
	GPIO_WRITE_PINS,
	UART_CONFIG,
	UART_IO_CONFIG,
	UART_TRANS,
	CRC_CUMULATE,
	CRC_CALCULATE,
	HOOK_USB_RX,
	HOOK_CMD_PROC,
	UART_CS_CONFIG,
	UART_CS,
	UART_COMMAND,
	HAL_SET_MUTE,
	I2C_TRANS,
	I2C_COMMAND,
	PUT_XUP_DATA,
	SET_BOARD_CALLS,
	HAL_EXEC_TABLE,
	GPIO_WAIT,
	DISABLE_IRQ,
	OUT_HEX,
	FIDX_MAX,
};

typedef struct tagSTDHAL {
	uint32_t (*mem_read)(const uint32_t addr);
	const char *mem_read_name;
	int32_t mem_read_fidx;
	void (*mem_write)(const uint32_t addr, const uint32_t value);
	const char *mem_write_name;
	int32_t mem_write_fidx;
	uint32_t (*mem_update)(const uint32_t addr, const uint32_t value, const uint32_t mask);
	const char *mem_update_name;
	int32_t mem_update_fidx;
	void (*isr_set)(const int32_t idx, const uint32_t handle);
	const char *isr_set_name;
	int32_t isr_set_fidx;
	int32_t (*isr_check)(uint32_t GPIO_Pin);
	const char *isr_check_name;
	int32_t isr_check_fidx;
	void (*decoder_config)(const uint32_t idx, const uint32_t vset0, const uint32_t vset1, const uint32_t vset2, const uint32_t vset3);
	const char *decoder_config_name;
	int32_t decoder_config_fidx;
	void (*decoder_write)(const uint32_t idx, const uint32_t val);
	const char *decoder_write_name;
	int32_t decoder_write_fidx;
	uint32_t (*rtc_read_backup_reg)(uint32_t BackupRegister);
	const char *rtc_read_backup_reg_name;
	int32_t rtc_read_backup_reg_fidx;
	void (*rtc_write_backup_reg)(uint32_t BackupRegister, uint32_t data);
	const char *rtc_write_backup_reg_name;
	int32_t rtc_write_backup_reg_fidx;
	int32_t (*gpio_config)(const uint32_t group, const uint32_t pin, const uint32_t mode, const uint32_t value, const uint32_t pull, const uint32_t alt, const uint32_t speed);
	const char *gpio_config_name;
	int32_t gpio_config_fidx;
	void (*gpio_write)(const uint32_t group, const uint32_t pin, const uint32_t value);
	const char *gpio_write_name;
	int32_t gpio_write_fidx;
	void (*gpio_toggle)(const uint32_t group, const uint32_t pin);
	const char *gpio_toggle_name;
	int32_t gpio_toggle_fidx;
	int32_t (*gpio_read)(const uint32_t group, const uint32_t pin);
	const char *gpio_read_name;
	int32_t gpio_read_fidx;
	void (*gpio_enable_irq)(const uint32_t irqn, const uint32_t ena, const uint32_t PreemptPriority, const uint32_t SubPriority);
	const char *gpio_enable_irq_name;
	int32_t gpio_enable_irq_fidx;
	int32_t (*i2c_config)(const uint32_t bus, uint32_t ClockSpeed, uint32_t scl_group, uint32_t scl_pin, uint32_t sda_group, uint32_t sda_pin);
	const char *i2c_config_name;
	int32_t i2c_config_fidx;
	void (*i2c_cs_config)(const uint32_t bus, const uint32_t value);
	const char *i2c_cs_config_name;
	int32_t i2c_cs_config_fidx;
	void (*i2c_cs)(const uint32_t bus, const uint32_t ch, const uint32_t val);
	const char *i2c_cs_name;
	int32_t i2c_cs_fidx;
	void (*i2c_enable)(const uint32_t bus, const int32_t ena);
	const char *i2c_enable_name;
	int32_t i2c_enable_fidx;
	int32_t (*i2c_write_ex)(const uint32_t bus, const uint32_t dev_addr, const uint32_t addr, const uint32_t addr_width, const uint32_t count, uint8_t * buf, const uint32_t timeout);
	const char *i2c_write_ex_name;
	int32_t i2c_write_ex_fidx;
	int32_t (*i2c_read_ex)(const uint32_t bus, const uint32_t dev_addr, const uint32_t addr, const uint32_t addr_width, const uint32_t count, uint8_t * buf, const uint32_t timeout);
	const char *i2c_read_ex_name;
	int32_t i2c_read_ex_fidx;
	int32_t (*i2c_update_ex)(const uint32_t bus, const uint32_t dev_addr, const uint32_t addr, const uint32_t addr_width, const uint32_t count, uint8_t * bufr, uint8_t * bufmsk, uint8_t * bufw, const uint32_t timeout);
	const char *i2c_update_ex_name;
	int32_t i2c_update_ex_fidx;
	int32_t (*spi_config)(const uint32_t bus, const uint32_t rx_only, const uint32_t data_8bit, const uint32_t is_clk_pol_h, const uint32_t is_clk_pha_1st, const uint32_t clk_div, const uint32_t msb_first);
	const char *spi_config_name;
	int32_t spi_config_fidx;
	void (*spi_cs_config)(const uint32_t bus, uint32_t value);
	const char *spi_cs_config_name;
	int32_t spi_cs_config_fidx;
	void (*spi_set_timing)(const uint32_t bus, const uint32_t is_clk_pol_h, const uint32_t is_clk_pha_2nd, const uint32_t clk_div);
	const char *spi_set_timing_name;
	int32_t spi_set_timing_fidx;
	void (*spi_enable)(const uint32_t bus, const int32_t ena);
	const char *spi_enable_name;
	int32_t spi_enable_fidx;
	void (*spi_cs_valid)(uint32_t ena);
	const char *spi_cs_valid_name;
	int32_t spi_cs_valid_fidx;
	void (*spi_cs)(uint32_t bus, uint32_t ch, uint32_t val);
	const char *spi_cs_name;
	int32_t spi_cs_fidx;
	int32_t (*spi_trans)(uint32_t bus, uint32_t tx_count, uint8_t *tx_data_all, uint32_t tx_byte_delay_us, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *spi_trans_name;
	int32_t spi_trans_fidx;
	int32_t (*spi_command)(uint32_t bus, uint32_t ch, uint32_t tx_count, uint8_t *tx_data_all, uint32_t tx_byte_delay_us, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *spi_command_name;
	int32_t spi_command_fidx;
	int32_t (*spi_command_ex)(uint32_t bus, uint32_t ch, uint32_t tx_count, uint8_t *tx_data_all, uint32_t tx_byte_delay_us, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t opt_cs, uint32_t timeout);
	const char *spi_command_ex_name;
	int32_t spi_command_ex_fidx;
	void (*vc_tx)(uint32_t len, const uint8_t *data);
	const char *vc_tx_name;
	int32_t vc_tx_fidx;
	void (*vc_printf)(const char* fmt, ...);
	const char *vc_printf_name;
	int32_t vc_printf_fidx;
	void (*ret_hex)(uint32_t count, uint8_t *buf);
	const char *ret_hex_name;
	int32_t ret_hex_fidx;
	void (*ret_bin_init)(uint8_t *xbuf);
	const char *ret_bin_init_name;
	int32_t ret_bin_init_fidx;
	void (*ret_bin)(uint32_t count, uint8_t *buf);
	const char *ret_bin_name;
	int32_t ret_bin_fidx;
	void (*delay_short)(uint32_t count);
	const char *delay_short_name;
	int32_t delay_short_fidx;
	void (*delay_init)(void);
	const char *delay_init_name;
	int32_t delay_init_fidx;
	void (*delay_us)(uint32_t au16_us);
	const char *delay_us_name;
	int32_t delay_us_fidx;
	void (*delay_ms)(uint32_t au16_ms);
	const char *delay_ms_name;
	int32_t delay_ms_fidx;
	uint8_t * (*str_skip_space)(uint8_t *data);
	const char *str_skip_space_name;
	int32_t str_skip_space_fidx;
	uint8_t * (*str_skip_char)(int ewz, uint8_t *data);
	const char *str_skip_char_name;
	int32_t str_skip_char_fidx;
	int32_t (*str_to_argv)(uint8_t *data, uint8_t *argv[], uint8_t **end);
	const char *str_to_argv_name;
	int32_t str_to_argv_fidx;
	uint32_t (*str_scan_uint)(uint8_t *str);
	const char *str_scan_uint_name;
	int32_t str_scan_uint_fidx;
	void (*sys_dfu_mode)();
	const char *sys_dfu_mode_name;
	int32_t sys_dfu_mode_fidx;
	int32_t (*gpio_read_pins)(const uint32_t group, const uint32_t pins);
	const char *gpio_read_pins_name;
	int32_t gpio_read_pins_fidx;
	void (*gpio_write_pins)(const uint32_t group, const uint32_t pins, const uint32_t value);
	const char *gpio_write_pins_name;
	int32_t gpio_write_pins_fidx;
	int32_t (*uart_config)(const uint32_t bus, const int32_t baud, uint32_t bits, uint32_t stop_bits, uint32_t parity);
	const char *uart_config_name;
	int32_t uart_config_fidx;
	int32_t (*uart_io_config)(const uint32_t bus, uint32_t ena, const uint32_t group_rx, const uint32_t pin_rx, const uint32_t group_tx, const uint32_t pin_tx);
	const char *uart_io_config_name;
	int32_t uart_io_config_fidx;
	int32_t (*uart_trans)(const uint32_t bus, uint32_t tx_count, uint8_t *tx_data_all, uint32_t cmd_byte_delay, uint32_t rx_count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *uart_trans_name;
	int32_t uart_trans_fidx;
	uint32_t (*crc_cumulate)(uint32_t addr, uint32_t len);
	const char *crc_cumulate_name;
	int32_t crc_cumulate_fidx;
	uint32_t (*crc_calculate)(uint32_t addr, uint32_t len);
	const char *crc_calculate_name;
	int32_t crc_calculate_fidx;
	uint32_t (*hook_usb_rx)(uint32_t f_addr);
	const char *hook_usb_rx_name;
	int32_t hook_usb_rx_fidx;
	uint32_t (*hook_cmd_proc)(uint32_t f_addr);
	const char *hook_cmd_proc_name;
	int32_t hook_cmd_proc_fidx;
	void (*uart_cs_config)(const uint32_t bus, uint32_t value);
	const char *uart_cs_config_name;
	int32_t uart_cs_config_fidx;
	void (*uart_cs)(uint32_t bus, uint32_t ch, uint32_t val);
	const char *uart_cs_name;
	int32_t uart_cs_fidx;
	int32_t (*uart_command)(uint32_t bus, uint32_t ch, uint32_t tx_count, uint8_t *tx_data_all, uint32_t tx_byte_delay_us, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *uart_command_name;
	int32_t uart_command_fidx;
	void (*hal_set_mute)(int8_t mute);
	const char *hal_set_mute_name;
	int32_t hal_set_mute_fidx;
	int32_t (*i2c_trans)(const uint32_t bus, uint32_t cmd_count, uint8_t *cmd, uint32_t cmd_delay, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *i2c_trans_name;
	int32_t i2c_trans_fidx;
	int32_t (*i2c_command)(uint32_t bus, uint32_t ch, uint32_t cmd_count, uint8_t *cmd, uint32_t cmd_delay, uint32_t count, uint8_t *buf, uint32_t rd_delay, uint32_t timeout);
	const char *i2c_command_name;
	int32_t i2c_command_fidx;
	int32_t (*put_xup_data)(int32_t ch, uint32_t *data, int32_t cnt);
	const char *put_xup_data_name;
	int32_t put_xup_data_fidx;
	int32_t (*set_board_calls)(uint32_t ftask, uint32_t fset_xup, uint32_t fset_xdn);
	const char *set_board_calls_name;
	int32_t set_board_calls_fidx;
	int32_t (*hal_exec_table)(const uint32_t *tbl);
	const char *hal_exec_table_name;
	int32_t hal_exec_table_fidx;
	uint32_t (*gpio_wait)(uint32_t group, const uint32_t pin, uint32_t st, uint32_t tmo, uint32_t disable_irq);
	const char *gpio_wait_name;
	int32_t gpio_wait_fidx;
	void (*disable_irq)(int32_t disable);
	const char *disable_irq_name;
	int32_t disable_irq_fidx;
	uint32_t gpio_groups;
	uint32_t uart_instances;
	uint32_t i2c_instances;
	uint32_t spi_instances;
	uint32_t hcrc;
	uint32_t *pSystemCoreClock;
	void (*out_hex)(uint32_t count, uint8_t *buf, uint32_t flag);
	const char *out_hex_name;
	int32_t out_hex_fidx;
}STDHAL;

typedef int32_t (*f_usb_rx)(uint8_t *Buf, uint32_t *Len);
//int32_t hook_usb_rx(uint32_t f_addr);

// return -1 means command not processed
typedef int32_t (*f_cmd_proc)(uint32_t cmd, uint8_t *buf, int32_t buf_size, int8_t *rep, uint32_t argc, uint8_t *argv[]);
//int32_t hook_cmd_proc(uint32_t f_addr);

typedef int32_t (*f_board_task)(void);
typedef int32_t (*f_board_set_xup)(uint32_t tasks, uint32_t extra);
typedef int32_t (*f_board_set_xdn)(uint32_t tasks, uint32_t extra);
//int32_t set_board_calls(uint32_t ftask, uint32_t fset_xup, uint32_t fset_xdn);

#ifdef __cplusplus
}
#endif

#endif /* INC_STD_HAL_H_ */
