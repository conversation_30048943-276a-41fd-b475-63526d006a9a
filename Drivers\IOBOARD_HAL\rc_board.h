#ifndef INC_RC_BOARD_H_
#define INC_RC_BOARD_H_

typedef struct tagBOARDRC
{
	const char *ver_str;
	int32_t (*fcmd)(uint32_t cmd, uint8_t *buf, int32_t buf_size, int8_t *rep, uint32_t argc, uint8_t *argv[]);
	int32_t (*finit)(void);
	int32_t (*ftask)(void);
	int32_t (*fset_xup)(uint32_t tasks, uint32_t extra);
	int32_t (*fset_xdn)(uint32_t tasks, uint32_t extra);
}BOARDRC;

#endif /* INC_RC_BOARD_H_ */
