#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq8900_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 基础设备配置函数
 *
 * 检查基础设备状态，读取产品ID，设置方向和看门狗定时器
 *
 * @param dir 初始化方向 (1为反向, 0为正向)
 * @return 0表示成功，负数表示错误
 */
int32_t shq_base_config(int8_t dir)
{    
    int32_t ret;
    uint8_t *p_ret;
    un_COMM_TIMEOUT_t comm_timeout;

    // 1. 检查设备是否就绪
    if (!dev_is_ready()) {
        print("inf:base not ready, clearing\n");
        dev_comm_clear();
        if (!dev_is_ready()) {
            print("inf:base error busy.\n");
            return COMME_BASE_ERROR;
        }
    }

    // 2. 读取并检查基础设备状态
    ret = single_dev_read(0, R_PRODUCT_ID, 1, &p_ret); // 0号设备，基础设备
    if (ret == 1) {
        print("inf:base read %02X\n", ((DSC_COMM_FRAME_HDR*)p_ret)->data[0]);
    } else {
        print("inf:base read error, ret=%d\n", ret);
        dev_comm_clear(); // 0号设备，基础设备
        return COMME_BASE_ERROR;
    }

    // 3. 设置默认方向为正向或反向
    shq_set_base_dir_bit(dir); // dir: 0为正向，1为反向

    // 4. 配置看门狗定时器
    comm_timeout.u8Register = 0;
#ifdef BASE_DIS_WDT
    comm_timeout.stcField.CTS_TIME = 0; // 禁用短超时
    comm_timeout.stcField.CTL_TIME = 0; // 禁用长超时
#else
    comm_timeout.stcField.CTL_ACT = 1;  // shutdown
    comm_timeout.stcField.CTL_TIME = 2; // 2秒
#endif
    single_dev_write(0, R_COMM_TIMEOUT, &comm_timeout.u8Register, 1); // 0号设备，基础设备

    // 5. 等待并最终检查
    simple_delay_ms(1); // 1ms延时，等待设备就绪
    if (!dev_is_ready()) { // 0号设备，基础设备
        print("inf:base not ready, clearing\n");
        dev_comm_clear(); // 0号设备，基础设备
        print("inf:base error busy.\n");
        return COMME_BASE_ERROR;
    }

    return 0;
}
