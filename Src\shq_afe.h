#ifndef HEADER_SHQ_AFE_H
#define HEADER_SHQ_AFE_H

#include <stdint.h>

#include "platform/comm_io.h" // for STACK_COUNT, INDEXED_CELLS_COUNT, and _STORE

#include "shq89718/shq89xx.h"

#define SHQ_DSC_BASE_SNUM 2 // set stack count to control base timeout, 1 + int((100*STACK_COUNT + 1500)/1850)
#define SHQ_DSC_MAX_TMO_USEC 3900 // > 1850*SHQ_DSC_BASE_SNUM + 200

#define ABS(x) (((x) > 0) ? (x) : -(x))

typedef enum
{
    DIAG_FAULT_OK = 0,
    // COMM_FAULT = -100,
	DIAG_FAULT_CVS_FAULT = -101,
    DIAG_FAULT_CELLLINE_OW = -102,
    DIAG_FAULT_GROUND_OW = -103,
    DIAG_FAULT_CSPIN_SHORT = -104,
    DIAG_FAULT_AUX_THR_FAULT = -105,
    DIAG_FAULT_NTC_OW = -106,
    DIAG_FAULT_NTC_SHORT_POWER = -107,
    DIAG_FAULT_NTC_SHORT_GND = -108,
    DIAG_FAULT_GPIO_PIN_SHORT = -109,
    DIAG_FAULT_POWER_BIST_FAIL = -110,
    DIAG_FAULT_COMP_BIST_FAIL = -111,
    DIAG_FAULT_STUCK_BIST_FAIL = -112,
    DIAG_FAULT_ICURR_POWER_FAIL = -113,
    DIAG_FAULT_FET_OPEN = -114,
    DIAG_FAULT_FET_SHORT = -115,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO2_GPIO1 = -116,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO2_GPIO3 = -117,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO6_GPIO5 = -118,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO6_GPIO7 = -119,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO10_GPIO9 = -120,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO10_GPIO11 = -121,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO4_GPIO3 = -122,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO4_GPIO5 = -123,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO8_GPIO7 = -124,
	DIAG_FAULT_GPIO_PIN_SHORT_GPIO8_GPIO9 = -125,
	DIAG_FAULT_CELL_WRIE_DISCONN = -126,
} DEV_FAULT_TYPE;


typedef struct {
    const uint8_t *cell_counts; /* 指向的数组保存每个AFE的电芯个数，不包括bus-bar，需要初始化 */
    const uint8_t *bus_bar_pos; /* 指向的数组保存每个AFE上的bus-bar位置，0表示没有bus-bar，需要初始化 */
    const uint8_t *gp_ch_counts;  /* 指向的数组保存每个AFE的gpio采样通道个数，需要初始化 */

#if defined(KEEP_ALL_DEV_VOLTS)
    int32_t all_dev_volts_vc[STACK_COUNT][18]; /* 按照设备保存所有电芯的电压 */
	int32_t all_dev_volts_cb[STACK_COUNT][18]; /* 按照设备保存均衡通道采集电压，暂时没读 */
	int32_t all_dev_volts_gp[STACK_COUNT][18]; /* 按照设备保存所有温度采样的电压 */

#if defined(KEEP_ADC_CODES)
    int16_t all_dev_code_vc[STACK_COUNT][18]; /* 按照设备保存所有电芯的电压 */
	int16_t all_dev_code_cb[STACK_COUNT][18]; /* 按照设备保存均衡通道采集电压，暂时没读 */
	int16_t all_dev_code_gp[STACK_COUNT][18]; /* 按照设备保存所有温度采样的电压 */
#endif
#endif // KEEP_ALL_DEV_VOLTS

#if defined(KEEP_INDEXED_VOLTS)
	int32_t indexed_vc_volts[INDEXED_CELLS_COUNT]; /* 保存所有电芯的电压 */
	int32_t indexed_cb_volts[INDEXED_CELLS_COUNT]; /* 保存均衡通道采集电压 */
	int32_t indexed_gp_volts[INDEXED_CELLS_COUNT]; /* 保存所有温度采样的电压 */
#if defined(KEEP_ADC_CODES)
    int16_t indexed_vc_code[INDEXED_CELLS_COUNT]; /* 按照设备保存所有电芯的电压 */
	int16_t indexed_cb_code[INDEXED_CELLS_COUNT]; /* 按照设备保存均衡通道采集电压，暂时没读 */
	int16_t indexed_gp_code[INDEXED_CELLS_COUNT]; /* 按照设备保存所有温度采样的电压 */
#endif
#endif // KEEP_INDEXED_VOLTS

    uint8_t stack_count; /* 系统设计的实际AFE数量, 需要初始化 */
    int8_t stack_err_addr_fwd; /* 当前正向通讯异常的AFE地址 */
    int8_t stack_err_addr_bwd; /* 当前反向通讯异常的AFE地址 */
    int8_t single_err_addr_fwd; /* single read 正向通讯异常的AFE地址 */
    int8_t single_err_addr_bwd; /* single read 反向通讯异常的AFE地址 */
    int8_t stack_count_fwd; /* 上次寻址后，实际可以通讯的正向链路AFE数量 */
    int8_t stack_count_bwd; /* 上次寻址后，实际可以通讯的反向链路AFE数量 */
    int32_t diag_afe_channels_fault[STACK_COUNT][MAX_CELLS_PER_AFE]; /* 保存所有afe所有通道的fault结果,错误类型 cvs_fault/aux_fault/cell_ow */

    // 诊断专用缓存区，与ADC采样结果分离
    int32_t diag_volts_vc[STACK_COUNT][MAX_CELLS_PER_AFE]; /* 诊断专用：电芯电压缓存 */
    int32_t diag_volts_cb[STACK_COUNT][MAX_CELLS_PER_AFE]; /* 诊断专用：均衡通道电压缓存 */
    int32_t diag_volts_gp[STACK_COUNT][MAX_CELLS_PER_AFE]; /* 诊断专用：GPIO温度电压缓存 */

	int8_t comm_dir_state;/*当前通讯方向，0表示正向，1表示反向*/
	int8_t dsc_stack_comm_err;
} SHQ_AFE_DATA;

#ifdef __cplusplus
extern "C"
{
#endif

int32_t shq_base_wakeup();
int32_t shq_base_config(int8_t dir); // dir: 0为正向，1为反向
int32_t shq_stack_wakeup(uintptr_t afe_ctx, int8_t dir);
int32_t shq_addressing(uintptr_t afe_ctx, int8_t dir);
int32_t shq_init(uintptr_t afe_ctx, int8_t dir);
int32_t shq_cs_sampling(uintptr_t afe_ctx, int8_t dir);
int32_t shq_gp_sampling(uintptr_t afe_ctx, int8_t dir);
int32_t shq_power_down();

// 诊断功能
int32_t diag_cell_wrie_disconnection(uintptr_t afe_ctx, int8_t dir);

int8_t shq_get_fw_stack_count(SHQ_AFE_DATA *p_afe);
int8_t shq_get_bw_stack_count(SHQ_AFE_DATA *p_afe);
int32_t shq_re_dir(SHQ_AFE_DATA *p_afe, int8_t dir); // dir: 0为正向，1为反向
int32_t shq_set_base_dir_bit(int8_t dir);
int32_t shq_set_dir_bit(int8_t dir);

#ifdef __cplusplus
}
#endif

#endif
