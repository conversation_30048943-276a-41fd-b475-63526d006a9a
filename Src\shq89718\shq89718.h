#ifndef SHQ89718_HDR
#define SHQ89718_HDR

#include <stdint.h>
#include <stdbool.h>
#include "shq89xx.h"

/*
    values for the "cell" parameter
*/
#define CELL_ALL	0
#define CELL_ODD	-1
#define CELL_EVEN	-2

/*
    bits for shq_set_adc_mode()
*/
#define CS_ADC_MODE_STOP	    0
#define CS_ADC_MODE_SINGLE	    1
#define CS_ADC_MODE_CONTINUOUS	2
#define CS_ADC_CBADC_ONCE	    3
#define GP_ADC_MODE_GPIO_SCAN	0
#define GP_ADC_MODE_GPIO1	    1
#define GP_ADC_MODE_GPIO2	    2
#define GP_ADC_MODE_GPIO3	    3
#define GP_ADC_MODE_GPIO4	    4
#define GP_ADC_MODE_GPIO5	    5
#define GP_ADC_MODE_GPIO6	    6
#define GP_ADC_MODE_GPIO7	    7
#define GP_ADC_MODE_GPIO8	    8
#define GP_ADC_MODE_GPIO9	    9
#define GP_ADC_MODE_GPIO10	    10
#define GP_ADC_MODE_GPIO11	    11
#define GP_ADC_MODE_VRES	    12
#define GP_ADC_MODE_VREF	    13
#define GP_ADC_MODE_DVDD	    14
#define GP_ADC_MODE_BATd40	    15
#define GP_ADC_MODE_AVDDd5	    16
#define GP_ADC_MODE_TS1		    18
#define GP_ADC_MODE_STOP	    0x1f

/*
    bits for return value of shq_get_adc_status()
*/
#define ADC_STA_BIT_FREEZE_ACTIVE	0x80
#define ADC_STA_BIT_CSADC_RUN		0x40
#define ADC_STA_BIT_AUX_RUN			0x20
#define ADC_STA_BIT_DRDY_LPF		0x10
#define ADC_STA_BIT_DRDY_SADC		0x08
#define ADC_STA_BIT_DRDY_CADC		0x04
#define ADC_STA_BIT_DRDY_AUX		0x02
#define ADC_STA_BIT_DRDY_AUX2		0x01


#ifdef __cplusplus
extern "C"
{
#endif

int32_t shq_reg_read(int8_t devAddress, uint16_t regAddress, uint8_t numBytes, uint8_t **pRetBuf);
int32_t shq_reg_write(int8_t devAddress, uint16_t regAddress, uint8_t regValue);
int32_t shq_reg_update(int8_t devAddress, uint16_t regAddress, uint8_t bitMask, uint8_t bitValue);

int32_t shq_get_id(int8_t dev_addr);
int32_t shq_set_wdt(int8_t dev_addr, bool enable);
int32_t shq_set_adc_filters(int8_t dev_addr, bool aux_osr_512, bool cs_osr_512, uint16_t vc_lpf_osr);
int32_t shq_set_adc_mode(int8_t dev_addr, bool all_freeze, int8_t cs_adc_mode, int8_t gp_adc_mode);
int32_t shq_set_adc_start(int8_t dev_addr, bool cs_adc_go, bool gp_adc_go);
int32_t shq_get_adc_status(int8_t dev_addr);

#ifdef __cplusplus
}
#endif

#endif
