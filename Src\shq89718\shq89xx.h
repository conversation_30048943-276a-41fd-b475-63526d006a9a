#ifndef SHQ89XX_HDR
#define SHQ89XX_HDR

#include <stdint.h>

#include "../platform/comm_io.h" // for MAX_STACKS_COUNT

#ifdef __cplusplus
extern "C"
{
#endif

typedef enum
{
    COMMC_SINGLE_READ = 0x00,
    COMMC_SINGLE_WRITE = 0x80 | 0x01,
	COMMC_STACK_READ = 0x02,
    COMMC_STACK_WRITE = 0x80 | 0x03,
	COMMC_BROADCAST_READ = 0x04,
	COMMC_BROADCAST_WRITE = 0x80 | 0x05,
	COMMC_BROADCAST_WRITE_REVERSE = 0x80 | 0x06,
} DSC_COMM_DEV_CMD;

typedef enum
{
	COMME_OK = 0,
	COMME_FAILED = -1,
	COMME_STACK_READ_ERROR = -2,
	COMME_CRC_FAILED = -3,
	COMME_FRAME_TYPE_UNKNOWN = -4,
	COMME_DEV_ADDR_UNKNOWN = -5,
	COMME_REG_ADDR_UNKNOWN = -6,
	COMME_DATA_SIZE_ERROR = -7,

	COMME_STACK_ERROR = -9,
	COMME_BASE_ERROR = -10,
	COMME_STACK_FWD_ERROR = -11,
	COMME_STACK_BWD_ERROR = -12,
	COMME_STACK_BOTH_ERROR = -13,
	COMME_NO_STACK_FWD_ERROR,
	COMME_NO_STACK_BWD_ERROR,

	COMME_SINGLE_READ_ERROR = -20,

	COMME_TRANS_FAILED = -30,
	COMME_TRANS_BUSY,
	COMME_TRANS_TIMEOUT,

	COMME_ADDRESSING_FAILED = -50,

	COMME_ADDRESSING_COUNT_FAILED = -50,

	COMME_NEED_MORE_TIME = -100,

	COMME_CURR_ABNORMAL = -200,
} DSC_COMM_DEV_ERROR;

// for data access
typedef struct
{
	union {
		uint8_t init_byte;
		int8_t  data_size_m1;
	};
	int8_t  dev_addr;
	uint8_t reg_addr_h;
	uint8_t reg_addr_l;
	uint8_t data[128];
} DSC_COMM_FRAME_HDR;

#define DSC_FRAME_DATA_OFFSET 4
#define DSC_RESP_NONE_DATA_BYTES 6

#define NO_DEV_ADDRESS -1
#define DEV_ADDR_BROADCAST -2
#define DEV_ADDR_STACKS	-3

int32_t shq_code_to_mV(int16_t code);
int32_t shq_code_to_0p1mV(int16_t code);
int32_t shq_code_to_0p01mV(int16_t code);
int32_t shq_code_to_temp(int16_t code);

uint8_t build_request(uint8_t* trans_buffer, DSC_COMM_DEV_CMD cmd, int8_t devAddress, uint16_t regAddress, const uint8_t* data, uint8_t dataSize);
int32_t check_response(uint8_t* trans_buffer, uint8_t frameSize, int8_t devAddress, uint16_t regAddress);
int32_t base_send_pulse(uint32_t width_us);
int32_t single_dev_read(int8_t devAddress, uint16_t regAddress, uint8_t numBytes, uint8_t **pRetBuf);
int32_t single_dev_write(int8_t devAddress, uint16_t regAddress, const uint8_t* data, const uint8_t dataSize);
int32_t broadcast_write(uint16_t regAddress, const uint8_t* data, uint8_t dataSize);
int32_t broadcast_write_reverse(uint16_t regAddress, const uint8_t* data, uint8_t dataSize);
int32_t stack_write(uint16_t regAddress, const uint8_t* data, uint8_t dataSize);
int32_t stack_read(uint16_t regAddress, uint8_t numBytes, uint8_t devCount, uint8_t **pRetBuf);
int32_t broadcast_read(uint16_t regAddress, uint8_t numBytes, uint8_t devCount, uint8_t **pRetBuf);

#ifdef __cplusplus
}
#endif

#endif // SHQ89XX_HDR
