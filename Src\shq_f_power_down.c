#include <stdint.h>
#include "platform/comm_io.h"
#include "shq89718/crc.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq8900_registers.h"
#include "shq89718/shq89718.h"
#include "shq_afe.h"
#include "shq_timer.h"

/**
 * @brief 系统掉电函数
 *
 * 通过发送掉电脉冲使基础/桥接设备和堆栈设备都掉电
 *
 * @return 0表示成功，负数表示错误
 */
int32_t shq_power_down()
{
    un_CONTROL1_t control1;
    control1.u8Register = 0;

    print("inf: 开始系统掉电流程\n");

    // 1. 等待3ms准备掉电
    print("inf: 1. 准备掉电\n");
    simple_delay_ms(3);

    // 2. 清除通信
    print("inf: 2. 清除通信\n");
    dev_comm_clear();

    // 3. 发送掉电脉冲，stack和base都掉电
    print("inf: 3. 发送掉电脉冲\n");
    control1.stcField.GOTO_SHUTDOWN = 1;
    broadcast_write(R_CONTROL1, &control1.u8Register, 1);

    // 4. 等待掉电脉冲完成
    print("inf: 4. 等待掉电脉冲完成\n");
    simple_delay_ms(47);

    // 5. 确认掉电脉冲完成
    while (!dev_is_long_pulse_done()) {
        delay_us(10);
    }

    print("inf: 系统掉电完成\n");
    return 0;
}
