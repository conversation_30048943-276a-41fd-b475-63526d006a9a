#ifndef SWI2C_HDR
#define SWI2C_HDR

#include <stdbool.h>
#include <stdint.h>

/*
 * bus: 16~19
 */
int32_t swi2c_config(uint16_t bus, uint16_t scl_group, uint16_t scl_pin, uint16_t sda_group, uint16_t sda_pin, uint32_t speed);
int32_t swi2c_mem_write(uint16_t bus, uint16_t DevAddress, uint16_t MemAddress,
                                    uint16_t MemAddSize, uint8_t *pData, uint16_t Size, uint32_t Timeout);
int32_t swi2c_mem_read(uint16_t bus, uint16_t DevAddress, uint16_t MemAddress,
                                    uint16_t MemAddSize, uint8_t *pData, uint16_t Size, uint32_t Timeout);

#endif
