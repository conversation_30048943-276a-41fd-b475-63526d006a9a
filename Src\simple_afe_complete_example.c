/*
 * 完整的简化AFE系统使用示例
 * 
 * 展示如何使用重写后的简化AFE系统，包括：
 * 1. 系统初始化
 * 2. 设备唤醒和配置
 * 3. 数据采样
 * 4. 错误处理
 * 5. 系统掉电
 */

#include "shq_afe_task.h"
#include "shq89718/shq89xx.h"
#include "shq89718/shq89718.h"
#include "shq_timer.h"

// 外部声明全局AFE数据
extern SHQ_AFE_DATA g_afe_data;

/**
 * @brief 完整的AFE系统示例
 * 
 * 演示从初始化到掉电的完整AFE操作流程
 */
void complete_afe_system_example(void)
{
    int32_t ret;
    
    print("=== 完整AFE系统示例开始 ===\n");
    
    // 1. 系统初始化
    print("1. 初始化AFE系统\n");
    shq_afe_task_init();
    
    // 2. 启动AFE系统
    // print("2. 启动AFE系统\n");
    // ret = cmd_proc('1dmc', NULL, 0, NULL, 0, NULL);
    // if (ret < 0) {
    //     print("启动AFE系统失败: %d\n", ret);
    //     return;
    // }
    
    // 3. 主运行循环
    print("3. 开始主运行循环\n");
    for (int cycle = 0; cycle < 10; cycle++) {
        print("--- 运行周期 %d ---\n", cycle + 1);
        
        ret = shq_afe_task_run();
        if (ret < 0) {
            print("AFE运行错误: %d，尝试重启\n", ret);
            shq_afe_simple_restart();
            
            // 重新启动
            // ret = cmd_proc('1dmc', NULL, 0, NULL, 0, NULL);
            // if (ret < 0) {
            //     print("重启失败，退出\n");
            //     break;
            // }
            continue;
        }
        
        print("周期 %d 完成\n", cycle + 1);
        
        // 模拟其他应用处理时间
        simple_delay_ms(100);
    }
    
    // 4. 停止AFE系统
    // print("4. 停止AFE系统\n");
    // ret = cmd_proc('0dmc', NULL, 0, NULL, 0, NULL);
    
    // 5. 系统掉电
    print("5. 系统掉电\n");
    ret = shq_power_down((uintptr_t)&g_afe_data);
    if (ret < 0) {
        print("系统掉电失败: %d\n", ret);
    }
    
    print("=== 完整AFE系统示例结束 ===\n");
}

/**
 * @brief 手动控制AFE各个步骤的详细示例
 */
void detailed_manual_control_example(void)
{
    int32_t ret;
    
    print("=== 详细手动控制示例开始 ===\n");
    
    // 初始化
    shq_afe_task_init();
    
    // 手动执行每个步骤
    print("1. 基础设备唤醒\n");
    ret = shq_base_wakeup((uintptr_t)&g_afe_data);
    if (ret < 0) {
        print("基础设备唤醒失败: %d\n", ret);
        return;
    }
    
    print("2. 基础设备配置\n");
    ret = shq_base_config(0);
    if (ret < 0) {
        print("基础设备配置失败: %d\n", ret);
        return;
    }
    
    print("3. 堆栈设备唤醒\n");
    ret = shq_stack_wakeup((uintptr_t)&g_afe_data, 0);
    if (ret < 0) {
        print("堆栈设备唤醒失败: %d\n", ret);
        return;
    }
    
    print("4. 地址分配\n");
    ret = shq_addressing((uintptr_t)&g_afe_data, 0);
    if (ret < 0) {
        print("地址分配失败: %d\n", ret);
        return;
    }
    
    print("5. 设备初始化\n");
    ret = shq_init((uintptr_t)&g_afe_data, 0);
    if (ret < 0) {
        print("设备初始化失败: %d\n", ret);
        return;
    }
    
    // 执行多次采样
    for (int i = 0; i < 5; i++) {
        print("--- 采样周期 %d ---\n", i + 1);
        
        print("6.%d 电芯电压采样\n", i + 1);
        ret = shq_cs_sampling((uintptr_t)&g_afe_data, 0);
        if (ret < 0) {
            print("电芯电压采样失败: %d\n", ret);
            continue;
        }
        
        print("7.%d GPIO/温度采样\n", i + 1);
        ret = shq_gp_sampling((uintptr_t)&g_afe_data, 0);
        if (ret < 0) {
            print("GPIO/温度采样失败: %d\n", ret);
            continue;
        }
        
        print("采样周期 %d 完成\n", i + 1);
        simple_delay_ms(50);
    }
    
    print("8. 系统掉电\n");
    ret = shq_power_down((uintptr_t)&g_afe_data);
    if (ret < 0) {
        print("系统掉电失败: %d\n", ret);
    }
    
    print("=== 详细手动控制示例结束 ===\n");
}

/**
 * @brief 错误处理和恢复示例
 */
void error_handling_example(void)
{
    int32_t ret;
    int error_count = 0;
    const int max_errors = 3;
    
    print("=== 错误处理和恢复示例开始 ===\n");
    
    shq_afe_task_init();
    
    // 启动系统
    // ret = cmd_proc('1dmc', NULL, 0, NULL, 0, NULL);
    // if (ret < 0) {
    //     print("初始启动失败\n");
    //     return;
    // }
    
    // 运行循环，模拟可能的错误
    for (int cycle = 0; cycle < 20; cycle++) {
        ret = shq_afe_task_run();
        
        if (ret < 0) {
            error_count++;
            print("错误 %d: AFE运行失败 (错误代码: %d)\n", error_count, ret);
            
            if (error_count >= max_errors) {
                print("错误次数过多，执行完整重启\n");
                
                // 完整重启序列
                shq_afe_simple_restart();
                simple_delay_ms(100);
                
                // ret = cmd_proc('1dmc', NULL, 0, NULL, 0, NULL);
                // if (ret < 0) {
                //     print("重启失败，退出\n");
                //     break;
                // }
                
                error_count = 0; // 重置错误计数
                print("系统重启成功\n");
            } else {
                print("尝试简单恢复\n");
                simple_delay_ms(50);
            }
        } else {
            // 成功运行，重置错误计数
            if (error_count > 0) {
                error_count = 0;
                print("系统恢复正常\n");
            }
        }
        
        simple_delay_ms(100);
    }
    
    // 清理
    // cmd_proc('0dmc', NULL, 0, NULL, 0, NULL);
    // shq_power_down((uintptr_t)&g_afe_data);
    
    print("=== 错误处理和恢复示例结束 ===\n");
}

/**
 * @brief 主函数示例
 */
int main(void)
{
    // 运行不同的示例
    
    // 1. 完整系统示例
    complete_afe_system_example();
    
    simple_delay_ms(1000);
    
    // 2. 详细手动控制示例
    detailed_manual_control_example();
    
    simple_delay_ms(1000);
    
    // 3. 错误处理示例
    error_handling_example();
    
    print("所有示例完成\n");
    return 0;
}

/*
 * 简化后的AFE系统特点总结:
 * 
 * 1. 简单直接的编程风格
 *    - 无状态机，无switch case
 *    - 无复杂指针操作，无复杂宏定义
 *    - 顺序执行，易于理解
 * 
 * 2. 完整的功能保留
 *    - 设备唤醒、配置、地址分配
 *    - 电芯电压采样、GPIO/温度采样
 *    - 错误处理、系统重启、掉电
 * 
 * 3. 统一的接口设计
 *    - 统一的返回值约定 (0成功，负数错误)
 *    - 简化的参数传递
 *    - 清晰的函数命名
 * 
 * 4. 增强的调试支持
 *    - 中文调试输出
 *    - 详细的执行流程跟踪
 *    - 清晰的错误信息
 * 
 * 5. 易于维护和扩展
 *    - 模块化设计
 *    - 清晰的代码结构
 *    - 简单的错误处理机制
 */
