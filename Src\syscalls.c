#include <sys/stat.h>

__attribute__((weak)) int _read(int file, char *ptr, int len)
{
	return len;
}

__attribute__((weak)) int _write(int file, char *ptr, int len)
{
	return len;
}

int _close(int file)
{
	return -1;
}


int _fstat(int file, struct stat *st)
{
	st->st_mode = S_IFCHR;
	return 0;
}

int _isatty(int file)
{
	return 1;
}

int _lseek(int file, int ptr, int dir)
{
	return 0;
}

int _open(char *path, int flags, ...)
{
	/* Pretend like we always fail */
	return -1;
}
