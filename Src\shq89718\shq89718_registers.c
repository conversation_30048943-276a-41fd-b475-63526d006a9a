#include <stdint.h>
#include <stdbool.h>

#include "shq89718_registers.h"

#if 0

RegConfig shq89718_regs[] =
{
	// address, read_mask, write_mask, default, current
	{R_PART_ID,		 0xff, 0x00, 0x09, 0x09},
	{R_COMM_CONF,	 0x7f, 0x7f, 0x00, 0x00},
	{R_COMM_STAT,	 0x03, 0x03, 0x00, 0x00}, // write 1 to clear
	{R_I2C_WR_DATA,	 0xff, 0xff, 0x00, 0x00},
	{R_I2C_CTRL,	 0x00, 0x3f, 0x00, 0x00}, // write auto clear
	{R_I2C_RD_DATA,	 0xff, 0x00, 0x00, 0x00},
	{R_SPI_CONF,	 0x7f, 0x7f, 0x00, 0x00},
	{R_SPI_TX3,		 0xff, 0xff, 0x00, 0x00},
	{R_SPI_TX2,		 0xff, 0xff, 0x00, 0x00},
	{R_SPI_TX1,		 0xff, 0xff, 0x00, 0x00},
	{R_SPI_EXE,		 0x00, 0x03, 0x02, 0x02}, // write auto clear
	{R_SPI_RX3,		 0xff, 0x00, 0x00, 0x00},
	{R_SPI_RX2,		 0xff, 0x00, 0x00, 0x00},
	{R_SPI_RX1,		 0xff, 0x00, 0x00, 0x00},
	{R_CONTROL2,	 0x1f, 0x1f, 0x00, 0x00},
	{R_DAISY_CTL1,	 0x03, 0x03, 0x01, 0x01},
	{R_DAISY_CTL2,	 0xff, 0xff, 0x22, 0x22},
	{R_I2C_SLAVE,	 0x01, 0x01, 0x00, 0x00},
	{R_BIST_GO,		 0x00, 0x0f, 0x00, 0x00}, // write auto clear
	{R_BIST_STAT,	 0xff, 0x00, 0x00, 0x00},
	{R_CB_CELL18_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL17_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL16_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL15_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL14_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL13_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL12_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL11_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL10_CTRL,0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL9_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL8_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL7_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL6_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL5_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL4_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL3_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL2_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_CB_CELL1_CTRL, 0x1f, 0x1f, 0x00, 0x00},
	{R_BAL_CTRL1,	 0x3f, 0x3f, 0x00, 0x00},
	{R_BAL_CTRL2,	 0x00, 0x0f, 0x00, 0x00}, // write auto clear
	{R_BAL_CTRL3,	 0x00, 0x3f, 0x00, 0x00}, // write auto clear
	{R_BAL_STAT,	 0x03, 0x00, 0x00, 0x00},
	{R_BAL_DONE1,	 0x03, 0x00, 0x00, 0x00},
	{R_BAL_DONE2,	 0xff, 0x00, 0x00, 0x00},
	{R_BAL_DONE3,	 0xff, 0x00, 0x00, 0x00},
	{R_BAL_TIME,	 0xff, 0x00, 0x00, 0x00},
	{R_ADC_CTRL1,	 0x3f, 0x3f, 0x00, 0x00},
	{R_ADC_CTRL2,	 0xff, 0xff, 0x00, 0x00},
	{R_ADC_CTRL3,	 0xff, 0xff, 0x00, 0x00},
	{R_ADC_CTRL4,	 0xff, 0xff, 0x00, 0x00},
	{R_ADC_CTRL5,	 0x00, 0x07, 0x00, 0x00}, // write auto clear
	{R_ADC_CTRL6,	 0xff, 0xff, 0x00, 0x00},
	{R_ADC_DBG1,	 0xff, 0xff, 0xFF, 0xFF},
	{R_ADC_DBG2,	 0xff, 0xff, 0xFF, 0xFF},
	{R_ADC_DBG3,	 0xff, 0xff, 0xFF, 0xFF},
	{R_ADC_DBG4,	 0xff, 0xff, 0xFF, 0xFF},
	{R_ADC_DBG5,	 0xff, 0xff, 0xF0, 0xF0},
	{R_ADC_STAT,	 0xff, 0x00, 0x00, 0x00},
	{R_ADC_CONF,	 0xff, 0xff, 0x00, 0x00},
	{R_SHUTDOWN_STAT,	 0x8f, 0x8f, 0x80, 0x80}, // write 1 to clear
	{R_BB_CONF,		 0xff, 0xff, 0x00, 0x00},
	{R_FSC_SCALE,	 0xff, 0xff, 0x40, 0x40},
	{R_C1V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C1V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C2V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C2V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C3V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C3V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C4V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C4V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C5V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C5V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C6V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C6V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C7V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C7V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C8V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C8V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C9V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C9V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C10V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C10V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C11V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C11V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C12V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C12V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C13V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C13V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C14V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C14V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C15V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C15V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C16V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C16V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C17V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C17V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_C18V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_C18V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC1_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC1_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC2_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC2_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC3_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC3_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC4_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC4_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC5_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC5_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC6_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC6_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC7_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC7_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC8_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC8_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC9_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC9_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC10_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC10_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC11_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC11_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC12_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC12_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC13_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC13_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC14_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC14_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC15_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC15_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC16_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC16_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC17_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC17_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_FC18_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_FC18_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S1V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S1V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S2V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S2V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S3V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S3V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S4V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S4V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S5V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S5V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S6V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S6V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S7V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S7V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S8V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S8V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S9V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S9V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S10V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S10V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S11V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S11V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S12V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S12V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S13V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S13V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S14V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S14V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S15V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S15V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S16V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S16V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S17V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S17V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_S18V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_S18V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP1V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP1V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP2V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP2V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP3V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP3V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP4V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP4V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP5V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP5V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP6V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP6V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP7V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP7V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP8V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP8V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP9V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_GP9V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_GP10V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_GP10V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_GP11V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_GP11V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_RG1V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG1V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG2V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG2V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG3V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG3V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG4V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG4V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG5V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG5V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG6V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG6V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG7V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG7V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG8V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG8V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG9V_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_RG9V_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_RG10V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_RG10V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_RG11V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_RG11V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_VRES_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_VRES_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_VREF2_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_VREF2_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_DVDD_1P8_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_DVDD_1P8_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_VBAT_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_VBAT_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_AVDD_5V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_AVDD_5V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_DVDD_5V_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_DVDD_5V_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_TS1_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_TS1_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_TS2_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_TS2_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_TS3_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_TS3_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_TS4_HI,		 0xff, 0x00, 0x00, 0x00},
	{R_TS4_LO,		 0xff, 0x00, 0x00, 0x00},
	{R_VREF1_HI,	 0xff, 0x00, 0x00, 0x00},
	{R_VREF1_LO,	 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S1,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S2,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S3,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S4,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S5,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S6,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S7,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S8,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S9,		 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S10,	 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S11,	 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S12,	 0xff, 0x00, 0x00, 0x00},
	{R_DIAG_S13,	 0xff, 0x00, 0x00, 0x00},
	{R_FAULT_SUMMARY,0xff, 0x00, 0x00, 0x00},
	{R_FUSE_CRC,	 0xff, 0x00, 0x00, 0x00},
	{R_OSC_CHECK,	 0xff, 0x00, 0x00, 0x00},
	{R_DIR0_ADDR,	 0x3f, 0x3f, 0x00, 0x00},
	{R_DIR1_ADDR,	 0x3f, 0x3f, 0x00, 0x00},
	{R_COMM_CTRL,	 0x03, 0x03, 0x00, 0x00},
	{R_CONTROL1,	 0x00, 0xff, 0x00, 0x00}, // write auto clear
	{R_OV_THRESH,	 0x7f, 0x7f, 0x7F, 0x7F},
	{R_UV_THRESH,	 0x3f, 0x3f, 0x00, 0x00},
	{R_GP1_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP1_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP2_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP2_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP3_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP3_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP4_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP4_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP5_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP5_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP6_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP6_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP7_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP7_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP8_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP8_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP9_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP9_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP10_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP10_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP11_OT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GP11_UT_TH,	 0x1f, 0x1f, 0x00, 0x00},
	{R_GPIO_CTRL1,	 0xff, 0xff, 0xFE, 0xFE},
	{R_GPIO_CTRL2,	 0x07, 0x07, 0x04, 0x04},
	{R_GPIO_CTRL3,	 0xff, 0xff, 0x00, 0x00},
	{R_GPIO_CTRL4,	 0x07, 0x07, 0x00, 0x00},
	{R_MASK_DIAG_S1, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S2, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S3, 0x1f, 0x1f, 0x00, 0x00},
	{R_MASK_DIAG_S4, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S5, 0x0f, 0x0f, 0x00, 0x00},
	{R_MASK_DIAG_S6, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S7, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S8, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S9, 0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S10,0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S11,0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S12,0xff, 0xff, 0x00, 0x00},
	{R_MASK_DIAG_S13,0x7f, 0x7f, 0x00, 0x00},
	{R_FAULT_MASK,	 0xff, 0xff, 0x00, 0x00},
	{R_CLR_FLT,		 0x00, 0xff, 0x00, 0x00}, // write auto clear
	{R_GPIO_STAT1,	 0xff, 0x00, 0x00, 0x00},
	{R_GPIO_STAT2,	 0xff, 0x00, 0x00, 0x00},
	{-1, 0, 0, 0, 0}
};
#endif
